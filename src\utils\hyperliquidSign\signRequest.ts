import {
  ethers,
  TypedDataDomain,
  Signature,
  TypedDataField,
} from 'ethers'
import {
  OperationType,
  ApproveAgentPayload,
  ApproveBuilderFeePayload,
  CancelOrdersPayload,
  SignatureResult,
} from './types'
import { Configs } from '@/const/configs'
import { toast } from 'sonner'


const HYPERLIQUID_CONFIG = Configs.getHyperliquidConfig()
const chainIdHex = HYPERLIQUID_CONFIG.chainIdHex
const network = HYPERLIQUID_CONFIG.env

type SignatureResultOrError = SignatureResult | { error: unknown }

export async function signRequest(
  signer: ethers.Signer,
  operation: OperationType,
  payload: ApproveAgentPayload | ApproveBuilderFeePayload | CancelOrdersPayload
): Promise<SignatureResultOrError> {
  const nonce = Date.now()

  const domain: TypedDataDomain = {
    name: 'HyperliquidSignTransaction',
    version: '1',
    chainId: parseInt(chainIdHex, 16),
    verifyingContract: '******************************************',
  }

  let types: Record<string, TypedDataField[]> = {}
  let message: Record<string, any> = {}

  try {
    if (operation === 'approveAgent') {
      const data = payload as ApproveAgentPayload
      types = {
        'HyperliquidTransaction:ApproveAgent': [
          { name: 'hyperliquidChain', type: 'string' },
          { name: 'agentAddress', type: 'address' },
          { name: 'agentName', type: 'string' },
          { name: 'nonce', type: 'uint64' },
        ],
      }

      message = {
        type: 'approveAgent',
        hyperliquidChain: capitalize(network),
        signatureChainId: chainIdHex,
        agentAddress: ethers.getAddress(data.agentAddress),
        agentName: data.agentName || '',
        nonce,
      }
    }

    if (operation === 'approveBuilderFee') {
      const data = payload as ApproveBuilderFeePayload
      types = {
        'HyperliquidTransaction:ApproveBuilderFee': [
          { name: 'hyperliquidChain', type: 'string' },
          { name: 'maxFeeRate', type: 'string' },
          { name: 'builder', type: 'address' },
          { name: 'nonce', type: 'uint64' },
        ],
      }

      message = {
        type: 'approveBuilderFee',
        hyperliquidChain: capitalize(network),
        signatureChainId: chainIdHex,
        maxFeeRate: data.maxFeeRate,
        builder: data.builder,
        nonce,
      }
    }

    const flatSignature = await signer.signTypedData(domain, types, message)
    const structured = Signature.from(flatSignature)

    return {
      action: message,
      nonce,
      signature: {
        r: structured.r,
        s: structured.s,
        v: structured.v,
      },
    }
  } catch (error) {
    toast.error(error)
    console.error('[signRequest] 签名失败:', error)
    return  {
      error: error
    }
  }
}

function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
