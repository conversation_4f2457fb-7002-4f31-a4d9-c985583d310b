import Text from '@/components/common/Text'
import { Button } from '@/components/ui/button'
import { Drawer, DrawerContent } from '@/components/ui/drawer'
import React, { memo } from 'react'
import { useTranslation } from 'react-i18next'

interface PriceInfoPanelProps {
  open: boolean
  setOpen: (open: boolean) => void
}

const PriceInfoPanel: React.FC<PriceInfoPanelProps> = ({ open, setOpen }) => {
  const { t } = useTranslation()

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto pb-6 bg-[url('/images/popup-bg.png')] bg-size-[100%_100%] bg-no-repeat">
        <div className="px-3 space-y-3 py-5 mt-3">
          {/* <DialogTitle className="">
            <Text text={'资金费率'} className="text-[calc(18rem/16)] pb-3" fontSize={18} />
          </DialogTitle> */}

          <Text text={t('orderBook.desc.theLatestPrice')} color="#FFFFFFB2" />
          <Text text={t('orderBook.desc.theIndexPrice')} color="#FFFFFFB2" />
          <Text text={t('orderBook.desc.theMarkPrice')} color="#FFFFFFB2" />

          <div className="flex justify-center items-center flex-row gap-2.5 mt-9">
            <Button
              variant="gradient"
              className="h-[44px] text-[#261236] rounded-[50px]"
              onClick={() => setOpen(false)}
            >
              {t('assets.overview.estimatedAssetsAgree')}
            </Button>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default memo(PriceInfoPanel)
