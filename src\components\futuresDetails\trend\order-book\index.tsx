import DrawerCheckSelect from '@/components/common/DrawerCheckSelect'
import { IconEmpty } from '@/components/icon'
import { useOrderBookData } from '@/hooks/hyperliquid/useOrderBookData'
import { cn } from '@/lib/utils'
import { coinOptionsSelector, symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { selectTiersBySymbol } from '@/redux/modules/futuresMeta.slice'
import { futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import {
  futuresTradePreferencesActions,
  selectFuturesTradePreferences,
} from '@/redux/modules/futuresTradePreferences.slice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { formatMoney } from '@/utils/helpers'
import { memo, useMemo, useState } from 'react'
import { DepthItem, OrderRecordProps, ProcessedDepthItem } from './type'
import PriceDisplay from './PriceDisplay'
import PriceInfoPanel from './PriceInfoPanel'

const sumDepth = (
  array: DepthItem[],
  // isReversed: boolean,
  depthUnit: 'base' | 'quote',
  szDecimals: number,
): ProcessedDepthItem[] => {
  const ordered = array

  let cumulative = 0
  const maxQuantity = ordered.reduce((max, item) => {
    cumulative += item.quantity
    return Math.max(max, cumulative)
  }, 0)

  cumulative = 0
  const processed = ordered.map((item) => {
    cumulative += item.quantity
    const fix = depthUnit === 'base' ? szDecimals : 0

    const quoteQuantity = Number((item.price * cumulative).toFixed(fix))
    const percent = maxQuantity > 0 ? Number(((cumulative / maxQuantity) * 100).toFixed(2)) : 0

    return {
      ...item,
      quantity: Number(cumulative.toFixed(fix)),
      quoteQuantity,
      percent,
    }
  })

  return processed
}

const OrderRecord = memo(
  ({ price, quantity, type, className, style, depthUnit }: OrderRecordProps) => (
    <div
      className={cn(
        'flex items-center justify-between gap-[10px] app-font-regular text-[calc(1rem*(11/16))] leading-[1] mb-[11.55px] last:mb-0 rounded-sm',
        className,
        type === 'bid' ? 'flex-row-reverse' : '',
      )}
      style={style}
    >
      <div className={cn('text-[#00FFB4]', type === 'ask' && 'text-[#AB57FF]')}>{price.toLocaleString()}</div>
      <div className="">{depthUnit === 'base' ? quantity : formatMoney(quantity)}</div>
    </div>
  ),
  (prevProps, nextProps) => {
    return (
      prevProps.price === nextProps.price &&
      prevProps.quantity === nextProps.quantity &&
      prevProps.quoteQuantity === nextProps.quoteQuantity &&
      prevProps.type === nextProps.type &&
      prevProps.className === nextProps.className &&
      JSON.stringify(prevProps.style) === JSON.stringify(nextProps.style)
    )
  },
)

OrderRecord.displayName = 'OrderRecord'

const OrderList = memo(
  ({ orders, type, depthUnit }: { orders: ProcessedDepthItem[]; type: 'bid' | 'ask'; depthUnit: 'base' | 'quote' }) => {
    return (
      <div className="">
        {orders.map((item, index) => (
          <OrderRecord
            key={`${type}-${index}-${item.price}-${item.quantity}`}
            price={item.price}
            quantity={depthUnit === 'base' ? item.quantity : item.quoteQuantity}
            quoteQuantity={item.quoteQuantity}
            type={type}
            style={{ '--after-width': `${item.percent}%`, '--after-radius': '2px' } as React.CSSProperties}
            className={cn('relative mb-0.5 py-1', type === 'bid' ? 'orderbook-buy-v2 pr-2' : 'orderbook-sell-v2 pl-2')}
            depthUnit={depthUnit}
          />
        ))}
      </div>
    )
  },
)

OrderList.displayName = 'OrderList'

const OrderBook = () => {
  const dispatch = useAppDispatch()
  const { baseCoin, quoteCoin, szDecimals } = useAppSelector(symbolInfoSelector)
  const { depthUnit } = useAppSelector(selectFuturesTradePreferences)
  const coinOptions = useAppSelector(coinOptionsSelector)

  const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))
  const tiers = useAppSelector(selectTiersBySymbol(baseCoin)) || []
  const depthTick = Number(tradeConfigs.depthTick)

  const [open, setOpen] = useState(false)

  const tierParams = useMemo(() => {
    const tier = tiers.find((item: any) => item.tick === depthTick)
    return {
      nSigFigs: tier?.nSigFigs,
      mantissa: tier?.mantissa,
    }
  }, [tiers, depthTick])

  const { bids, asks } = useOrderBookData(baseCoin, tierParams.nSigFigs, tierParams.mantissa)

  const handleDepthUnitChange = (value: string) => {
    if (value === showDepthUnit) return
    dispatch(
      futuresTradePreferencesActions.updateTradePreferences({
        depthUnit: baseCoin === value ? 'base' : 'quote',
      }),
    )
  }

  const topAsks = useMemo(() => sumDepth(asks.reverse(), depthUnit, szDecimals), [asks, depthUnit, szDecimals])
  const topBids = useMemo(() => sumDepth(bids, depthUnit, szDecimals), [bids, depthUnit, szDecimals])

  const showDepthUnit = useMemo(() => {
    return depthUnit === 'base' ? baseCoin : quoteCoin
  }, [depthUnit, baseCoin, quoteCoin])

  const tableHeader = useMemo(
    () => (
      <div className="flex px-3 py-2 justify-between">
        <div className="flex-1 text-[#FFFFFF80] app-font-medium text-[calc(1rem*(11/16))]">数量 ({baseCoin})</div>
        <div className="flex-1 text-[#FFFFFF80] app-font-medium text-[calc(1rem*(11/16))] text-center">
          价格 ({quoteCoin})
        </div>
        <DrawerCheckSelect
          childrenTrigger={
            <div className="flex-1 text-[#FFFFFF80] app-font-medium text-[calc(1rem*(11/16))] text-right cursor-pointer flex justify-end">
              数量 ({showDepthUnit}) <img src="/images/futuresDetail/select-down-icon.svg" className="" />
            </div>
          }
          options={coinOptions}
          value={showDepthUnit}
          onChange={(value: string) => handleDepthUnitChange(value)}
        />
      </div>
    ),
    [baseCoin, quoteCoin, showDepthUnit],
  )

  return (
    <div>
      {/* <PriceOverviewPanel /> */}

      <div className="rounded-md">
        <PriceDisplay setOpen={setOpen} />
        {tableHeader}

        <div className="flex flex-col overflow-y-auto">
          <div className="grid grid-cols-2 gap-1 px-3">
            <OrderList orders={topBids} type="bid" depthUnit={depthUnit} />
            <OrderList orders={topAsks} type="ask" depthUnit={depthUnit} />
          </div>
          {bids.length === 0 && asks.length === 0 && (
            <div className="flex flex-col items-center justify-center h-80">
              <IconEmpty />
              <span className="text-[#FFFFFF80] text-[0.75rem]">没有数据</span>
            </div>
          )}
        </div>
      </div>
      <PriceInfoPanel open={open} setOpen={setOpen} />
    </div>
  )
}

export default memo(OrderBook)
