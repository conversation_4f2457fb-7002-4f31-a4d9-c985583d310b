import { OrderType, TransactionType } from '@/@generated/gql/graphql-trading'
import Container from '@components/common/Container.tsx'
import { useState, useCallback, useMemo } from 'react'
import MyPositionCard from './MyPositionCard'
import MyPositionFilter from './MyPositionFilter'
import { xPositions, xOpenOrders, OrderSide, All } from '../types'
import { IconEmpty } from '@/components/icon'
import { useAppSelector } from '@/redux/store'
import { selectAllPerpMeta } from '@/redux/modules/futuresMeta.slice'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { signHyperLiquidCreateOrderMutation } from '@services/auth.service.ts'
import { toast } from 'sonner'
import { Configs } from '@/const/configs'
import { isHyperOrderSuccess, getAdjustedTriggerPrice } from '../tools'
import { selectSzMap } from '@/redux/modules/futuresMeta.slice'
import { builderSelector, agentWalletSelector } from '@/redux/modules/futuresUserInfo.slice'
import { privateKeyToAccount } from 'viem/accounts'
import { signStandardL1Action } from '../../hooks/signing'
import useCustomToast from '@/hooks/useCustomToast'







export type OrdersListFilterOrderType = 'All' | OrderType

export type OrdersListFilter = {
  showOnlyBaseCoin: boolean
  side?: OrderSide | All
}

interface MyPositionListProps {
  positions: xPositions[]
  baseCoin: string
}

const MyPositionList = ({ positions, baseCoin }: MyPositionListProps) => {
  const { showToast } = useCustomToast()
  const agentWallet = useAppSelector(agentWalletSelector)

  const allMeta = useAppSelector(selectAllPerpMeta)
  const szMap = useAppSelector(selectSzMap)

  const builder = useAppSelector(builderSelector)

  const [filter, setFilter] = useState<OrdersListFilter>({
    showOnlyBaseCoin: false,
    side: 'All'
  })


  const filterPostions = useMemo(() => {
    if (filter.side === 'All' && !filter.showOnlyBaseCoin) {
      return positions
    }
    return positions.filter(item => {
      return (item.side === filter.side || (filter.side === 'All'))
        && (!filter.showOnlyBaseCoin || (baseCoin === item.coin))
    })

  }, [filter, positions])


  const handleClickCloseAll = async () => {
    if (!positions.length || !agentWallet?.privateKey) return


    const orders = positions.map(position => {
      const newSize = Math.abs(parseFloat(position.szi))

      const newPrice = getAdjustedTriggerPrice(position.side, position.midPrice, true, szMap[position.coin])

      return {
        a: allMeta.findIndex(item => (position.coin === item.name)),
        b: position.side === 'B' ? false : true,
        p: newPrice,
        s: newSize.toString(),
        r: true,
        t: { limit: { tif: "FrontendMarket" } },
      }
    });
    const nonce = Date.now()

    const orderAction = {
      "type": "order",
      orders: orders,
      grouping: "na",
      builder
    }
    if (!agentWallet) return

    console.log('agentWallet.privateKey', agentWallet.privateKey)
    const wallet = privateKeyToAccount(agentWallet.privateKey)

    const signature = await signStandardL1Action(orderAction, wallet, null, nonce)


   

    const response = await fetch(`${Configs.getHyperliquidConfig().apiUrl}/exchange`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: orderAction,
        nonce: nonce,
        signature: signature,
        vaultAddress: null
      })
    })

    const result = isHyperOrderSuccess(await response.json())
    if (!result.ok) {
      showToast(result?.error as string)
      return
    }
    showToast('一键平仓成功')



  }

 
  return (
    <Container className="mt-[10px]">
      <MyPositionFilter
        filter={filter}
        setFilter={setFilter}
        onClickCloseAll={handleClickCloseAll}
      />
      <div className="flex flex-col gap-1.5">
        {
          filterPostions.map(item => (
            <MyPositionCard
              positionInfo={item}
              key={item.coin}
            />
          ))
        }
        {filterPostions?.length === 0 && (
          <div className="flex flex-col items-center justify-center h-80">
            <IconEmpty />
            <span className="text-[#FFFFFF80] text-[0.75rem]">没有数据</span>
          </div>
        )}
      </div>

    </Container>
  )
}

export default MyPositionList
