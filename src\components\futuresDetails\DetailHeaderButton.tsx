import { symbolDexClient } from '@/lib/gql/apollo-client'
import { ServiceConfig } from '@/lib/gql/service-config'
import { GET_FAVORITE_SYMBOLS, UPSERT_FAVORITE_SYMBOL } from '@/services/symbol.dex.service'
import DetailHeaderButton from '@components/detailHeader/DetailHeaderButton.tsx'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { toast } from 'sonner'

interface HeaderButtonProps {
  token: string
}

const HeaderButton = ({ token }: HeaderButtonProps) => {
  const { t } = useTranslation()
  const [isFavorite, setIsFavorite] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const handleUpsertFavorite = async (symbol: string[], isFavorite: boolean) => {
    try {
      const { data } = await symbolDexClient.mutate({
        mutation: UPSERT_FAVORITE_SYMBOL,
        variables: {
          input: {
            symbol,
            isFavorite,
          },
        },
      })
      if (data?.error) {
        toast.error(t('common.error'), {
          duration: 3000,
        })
        return
      }
      if (isFavorite) {
        setIsFavorite(true)
        toast.error(t('toast.addFavoriteSuccess'), {
          duration: 3000,
        })
        return
      }
      toast.error(t('toast.removeFavoriteSuccess'), {
        duration: 3000,
      })
      setIsFavorite(false)
    } catch (err: any) {
      toast.error(t(err[0].message), {
        duration: 3000,
      })
      return { success: false, error: 'Network error occurred' }
    }
  }

  const handleCollectChange = (event: React.MouseEvent) => {
    event.stopPropagation()

    if (!ServiceConfig.token) {
      toast.warning(t('listCoin.requireLoginToFavorite'), { position: 'top-center' })
    } else {
      handleUpsertFavorite([token], !isFavorite)
    }
  }

  const getFavoriteSymbols = async () => {
    try {
      const { data, loading } = await symbolDexClient.query({
        query: GET_FAVORITE_SYMBOLS,
      })
      setIsLoading(loading)
      if (data?.getFavoriteSymbols?.list?.find((item: any) => item?.symbol === token)) {
        setIsFavorite(true)
      } else {
        setIsFavorite(false)
      }
    } catch (error) {
      console.error('Error fetching favorite symbols:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    getFavoriteSymbols()
  }, [token])

  return (
    <div className="pr-[10px]">
      {!isLoading && (
        <DetailHeaderButton
          className="transition-all duration-100 hover:scale-[1.1] mr-3"
          icon={isFavorite ? '/images/icons/star-active-icon.svg' : '/images/detailHeader/icon-star.svg'}
          onClick={handleCollectChange}
        />
      )}

      <DetailHeaderButton
        className="transition-all duration-100 hover:scale-[1.1]"
        icon="/images/detailHeader/icon-share.svg"
      />
    </div>
  )
}
export default HeaderButton
