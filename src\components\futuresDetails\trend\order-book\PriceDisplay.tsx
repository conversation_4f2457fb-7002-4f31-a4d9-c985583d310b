
import { symbolInfoSelector,setSymbolInfo } from '@/redux/modules/futuresCurrentSymbol.slice'
import { useAppSelector,useAppDispatch } from '@/redux/store'
import { memo, useEffect, useMemo, useRef } from 'react'
import DrawerCheckSelect from '@components/common/DrawerCheckSelect.tsx'
import { futuresTradeConfigActions,futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import { selectTiersBySymbol } from '@/redux/modules/futuresMeta.slice'
import { useOrderBookData } from '@/hooks/hyperliquid/useOrderBookData'
import { useActiveAssetCtx } from '@/hooks/hyperliquid/useActiveAssetCtx'
import { useOrderTradeData } from '@/hooks/hyperliquid/useOrderTradeData'


const PriceDisplay = ({setOpen}: {setOpen: React.Dispatch<React.SetStateAction<boolean>>}) => {
  const dispatch = useAppDispatch()
  const { baseCoin,markPrice, lastTradePrice } = useAppSelector(symbolInfoSelector)
  const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))
  const tick = Number(tradeConfigs.depthTick)
  const tiers = useAppSelector(selectTiersBySymbol(baseCoin)) || []
  const { price } = useActiveAssetCtx(baseCoin)
  // 缓存上一次价格
  const lastPriceRef = useRef<string | null>(null);
  
  // 使用 useOrderTradeData 获取最新交易数据
  const trades = useOrderTradeData(baseCoin);
  
  // 当交易数据更新时，更新最新成交价
  useEffect(() => {
    if (trades.length > 0) {
      const latestTrade = trades[0];
      const newPrice = String(latestTrade.price);
      
      // 只有价格发生变化时才更新
      if (newPrice !== lastPriceRef.current) {
        lastPriceRef.current = newPrice;
        dispatch(setSymbolInfo({ lastTradePrice: newPrice }));
      }
    }
  }, [trades, dispatch]);

  // 获取订单数据
  const tierParams = useMemo(() => {
    const tier = tiers.find(t => Number(t.tick) === tick)
    return tier ? { nSigFigs: tier.nSigFigs, mantissa: tier.mantissa } : { nSigFigs: null, mantissa: null }
  }, [tiers, tick])

  const { bids, asks } = useOrderBookData(baseCoin, tierParams.nSigFigs, tierParams.mantissa)
  // 计算买入卖出占比
  const { buyPercentage, sellPercentage } = useMemo(() => {
    if (!bids.length && !asks.length) {
      return { buyPercentage: 50, sellPercentage: 50 }
    }
    // 计算总量（取前20档数据）
    const topBids = bids.slice(0, 20)
    const topAsks = asks.slice(0, 20)
    const totalBidVolume = topBids.reduce((sum, bid) => sum + bid.quantity, 0)
    const totalAskVolume = topAsks.reduce((sum, ask) => sum + ask.quantity, 0)
    const totalVolume = totalBidVolume + totalAskVolume

    if (totalVolume === 0) {
      return { buyPercentage: 50, sellPercentage: 50 }
    }

    const buyPct = Math.round((totalBidVolume / totalVolume) * 100)
    const sellPct = 100 - buyPct

    return { buyPercentage: buyPct, sellPercentage: sellPct }
  }, [bids, asks])

  	const depthOptions = useMemo(() => {
		if (!tiers?.length) return []
		return tiers?.map(item => {
			return {
				label: item.tick,
				value: item.tick,
			}
		})
	}, [tiers])
  const StatisticItem = memo<{
    label: string
    value: string
    className?: string
    onClick?: () => void
  }>(({ label, value, onClick }) => (
     <div className="text-center" >
        <div className="text-[#FFFFFF50] text-[calc(11rem/16)] cursor-pointer" onClick={onClick}>{label}</div>
        <div className="text-white text-[calc(11rem/16)]">{value}</div>
      </div>
  ))
    const handleTickChange = (tick: string) => {
      dispatch(futuresTradeConfigActions.updateFuturesTradeConfig({
        symbol: baseCoin,
        config: {
          depthTick: (Number(tick))
        }
      }))
    }

  return (
    <div className=" text-white pl-3 pr-3 rounded-lg">
      <div className="flex justify-between mb-[10px]">
        <StatisticItem label="最新价格" value={lastTradePrice ?? '--'} onClick={() => setOpen(true)}></StatisticItem>
        <StatisticItem label="指数价格" value={ String(price) ?? '--'} onClick={() => setOpen(true)}></StatisticItem>
        <StatisticItem label="标记价格" value={markPrice ?? '--'} onClick={() => setOpen(true)}></StatisticItem>
      </div>
      <div className="space-y-2">
        <div className="flex items-center">
          {/* 左侧买入文案 */}
          <span className="text-[#00FFB4] text-[calc(11rem/16)] min-w-[40px]">B {buyPercentage}%</span>

          {/* 中间进度条 */}
          <div className="relative h-[12px] rounded-2xl overflow-hidden flex-1 bg-[#1A1A1A]">
            <div
              className="absolute left-0 top-0 h-full bg-[#00FFB4]/40"
              style={{
                width: `${buyPercentage}%`,
                borderRight:'2px solid #1A1A1A',
                // clipPath: 'polygon(0 0, 100% 0, 98% 100%, 0 100%)',
              }}
            />
            <div
              className="absolute right-0 top-0 h-full bg-[#AB57FF]/40"
              style={{
                width: `${sellPercentage}%`,
                // borderRight:'2px solid #00FFB4',
                //  clipPath: 'polygon(2% 0, 100% 0, 100% 100%, 0% 100%)',
              }}
            />
          </div>

          {/* 右侧卖出文案 */}
          <span className="text-[#AB57FF] text-[calc(11rem/16)] min-w-[40px] text-right">{sellPercentage}% S</span>
        </div>

  
        <div className="flex justify-between items-center text-[#FFFFFF80] text-[calc(12rem/16)]">
          <span>买入</span>
          <span>卖出</span>
          <div className="flex items-center">
            <DrawerCheckSelect
              childrenTrigger={
                <div className='flex-1 flex items-center justify-between  py-1 px-2 rounded-[4px] bg-[#ECECED14] 
                  text-[calc(1rem*(12/16))] leading-[calc(1rem*(12/16))] text-[#FFFFFFB2] cursor-pointer'>
                  {tick}
                  <img className='ml-2' src="/images/futuresDetail/s-down-arrow-icon.svg" />
                </div>
              }
              optionClassName="border-b-[0.5px] border-solid border-[#ECECED14]"
              options={depthOptions as any}
              value={tick as any}
              onChange={handleTickChange}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default PriceDisplay
