import ActivityTable from '@/components/assets/wallet/ActivityTable'
import Analysis from '@/components/assets/wallet/Analysis'
import Distribution from '@/components/assets/wallet/Distribution'
import HoldingTable from '@/components/assets/wallet/HoldingTable'
import ChainCurrencyIcon from '@/components/common/ChainCurrencyIcon'
import { MoneyFormattedOneTime } from '@/components/common/MoneyFormatted'
import WalletTypeBadge from '@/components/common/WalletTypeBadge'
import HeaderWithBack from '@/components/header/HeaderWithBack'
import { Button } from '@/components/ui/button'
import useGetTotalFollowingAddress from '@/hooks/useGetTotalFollowingAddress'
import { TYPE_CHAIN } from '@/lib/blockchain'
import { futureClient, gqlClient } from '@/lib/gql/apollo-client'
import { cn } from '@/lib/utils'
import { setDataUnit } from '@/redux/modules/userSettings.slice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { getSmartMoneyInfo, getSmartMoneyStatistic } from '@/services/copytrade.service'
import { followWallet, unFollowWallet } from '@/services/wallet.service'
import { UITab } from '@/types/uiTabs.ts'
import { formatNumber } from '@/utils/helpers'
import { listCoinHelper } from '@/utils/list-coin-helper'
import { checkIsNumber, formatPercent } from '@/utils/numbers'
import { getTotalKeysInObject } from '@/utils/object'
import { covertSeconds } from '@/utils/time'
import { useQuery } from '@apollo/client'
import FilterTime, { FilterTimeOption } from '@components/common/FilterTime'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import { get } from 'lodash-es'
import { FC, Fragment, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import { toast } from 'sonner'

const filterTimeOptions: FilterTimeOption[] = [
  {
    value: '1',
    unit: 'D',
  },
  {
    value: '7',
    unit: 'D',
  },
  {
    value: '30',
    unit: 'D',
  },
]

const mockupDistribution = [
  {
    lv: 5,
    tradedAmount: '2',
    profitRate: '0.4',
  },
  {
    lv: 4,
    tradedAmount: '544',
    profitRate: '0.8',
  },
  {
    lv: 3,
    tradedAmount: '34',
    profitRate: '0.3',
  },
  {
    lv: 2,
    tradedAmount: '9',
    profitRate: '0.5',
  },
  {
    lv: 1,
    tradedAmount: '2',
    profitRate: '0.15',
  },
]

const WalletInfo: FC<{ address: string }> = ({ address }) => {
  const { t } = useTranslation()
  const [isFollowing, setIsFollowing] = useState(false);

  const { data } = useQuery(getSmartMoneyInfo, {
    variables: { req: { address, chain: "SOLANA" } },
    client: futureClient,
  })
  const { data: totalData } = useGetTotalFollowingAddress()
  useEffect(() => {
    const followingAddresses = get(totalData, 'getFollowingWalletAddressess', [] as string[]);
    setIsFollowing(followingAddresses.includes(address))
  }, [totalData])

  const currentWallet = useMemo(() => {
    if (data) {
      return data.getSmartMoneyInfo
    }
    return {
      address: '',
      name: 'Unknown Wallet',
      avatar: '/images/icons/default-avatar.png',
      tags: []
    }
  }, [data])
  const handleFlow = (follow: boolean) => {
    gqlClient.mutate(
      {
        mutation: follow ? followWallet : unFollowWallet,
        variables: {
          input: {
            walletAddress: address,
            chain: "SOLANA",
          }
        }
      }
    ).then(() => {
      toast.success(follow ? t('walletDetail.msg.flow') : t('walletDetail.msg.unflow'), { position: 'bottom-center' })
      setIsFollowing(follow)
    })
      .catch(() => {
        toast.warning(t('toast.addFavoriteFailed'), { position: 'bottom-center' })
      })
  }

  return (
    <div className="flex items-center justify-between gap-2 py-2.5 h-12">
      <div className="flex items-center">
        <ChainCurrencyIcon currencyIcon={get(currentWallet, 'avatar')}
          avatarClassName="w-[44px] h-[44px] aspect-square flex justify-center items-center rounded-sm"
          className="mr-3 flex items-center justify-center" fallbackImageEnable />
        <div>
          <div className="flex items-center gap-[6px] mb-2">
            <div className="mt-1 font-normal text-[18px] leading-none text-white/70">
              {currentWallet.address?.length > 5
                ? `${listCoinHelper.formatWalletNameCustom(currentWallet.address)}`
                : currentWallet.address}
            </div>
            <img
              src="/images/icons/icon-copy2.svg"
              className="w-4 h-4 cursor-pointer hover:scale-[1.1]"
              alt="copy"
              onClick={() => {
                navigator.clipboard.writeText(currentWallet.address)
                toast.success(t('toast.copiedSuccess'), { position: 'top-center' })
              }}
            />
            {/* <img
            src="/images/icons/edit.svg"
            className="w-4 h-4 cursor-pointer hover:scale-[1.1]"
            alt="edit"
            onClick={() => { }}
          /> */}
          </div>
          <WalletTypeBadge type={currentWallet.tags} />
        </div>
      </div>
      <div>
        <Button
          className={cn('gap-0 px-2 rounded-full relative transition-all duration-100 hover:scale-[1] min-w-[56px] h-[24px] text-xs', isFollowing ? 'text-white' : 'text-black')}
          variant={isFollowing ? 'borderGradient' : 'gradient'}
          onClick={() => handleFlow(!isFollowing)}
        >{isFollowing ? t('walletDetail.btn.unfollow') : t('walletDetail.btn.follow')}</Button>
      </div>
    </div>
  )
}

const WalletStatistics: FC<{ address: string, duration: number, dataUnit: 'USD' | 'SOL' | 'ETH' }> = ({ address, duration = 7, dataUnit = 'USD' }) => {
  const [balanceSol, setBalanceSol] = useState(0);
  const [distribution, setDistribution] = useState(mockupDistribution);
  const { data, loading } = useQuery(getSmartMoneyStatistic, {
    variables: { input: { address, chain: "SOLANA", dayDuration: duration } },
    client: futureClient,
  })
  const currentWallet = useMemo(() => {
    if (data) {
      return data.getSmartMoneyStatistic
    }
    return {}
  }, [data]);
  useEffect(() => {
    if (currentWallet?.tokenHoldings && currentWallet.tokenHoldings.length > 0) {
      currentWallet.tokenHoldings.forEach((holding: {
        address: string;
        balance: number;
        avgPriceUsd: string;
      }) => {
        if (holding.address === 'So11111111111111111111111111111111111111112') {
          setBalanceSol(holding.balance);
        }
      })
    }
    if (currentWallet) {
      const _total = getTotalKeysInObject(currentWallet, ['pnlGt5xNum', 'pnl2xTo5xNum', 'pnlLt2xNum',
        'pnlLtMinusDot5Num', 'pnlMinusDot5To0xNum']);
      const _distribution = [
        {
          lv: 5,
          tradedAmount: String(get(currentWallet, 'pnlGt5xNum', '0')),
          profitRate: String(_total ? (currentWallet.pnlGt5xNum / _total) : 0),
        },
        {
          lv: 4,
          tradedAmount: String(get(currentWallet, 'pnl2xTo5xNum', '0')),
          profitRate: String(_total ? (currentWallet.pnl2xTo5xNum / _total) : 0),
        },
        {
          lv: 3,
          tradedAmount: String(get(currentWallet, 'pnlLt2xNum', '0')),
          profitRate: String(_total ? (currentWallet.pnlLt2xNum / _total) : 0),
        },
        {
          lv: 2,
          tradedAmount: String(get(currentWallet, 'pnlMinusDot5To0xNum', '0')),
          profitRate: String(_total ? (currentWallet.pnlMinusDot5To0xNum / _total) : 0),
        },
        {
          lv: 1,
          tradedAmount: String(get(currentWallet, 'pnlLtMinusDot5Num', '0')),
          profitRate: String(_total ? (currentWallet.pnlLtMinusDot5Num / _total) : 0),
        },
      ];
      setDistribution(_distribution);
    }
  }, [data])
  const _endPercent = currentWallet.realizedPnlUsd / currentWallet.buyAmountUsd;
  console.log('currentWallet', currentWallet);
  const isUSD = dataUnit === 'USD';
  return (
    <Fragment>
      <div className="mt-2 flex items-end gap-[6px]">
        <div className="font-bold text-[32px] text white leading-none break-all">
          <span className='inline-block text-center'>
            {checkIsNumber(_endPercent) ? _endPercent * 100 > 1e5 ? '>' : _endPercent * 100 > 0 ? '+' : '-' : ''}
          </span>
          {formatPercent(Math.abs(_endPercent * 100), 2, '--', false)}
        </div>
        <div className="text-[16px] text-white/70 leading-[1.4]">%</div>
        <div className="text-[15px] text-[#00FFB4] leading-none ml-0.5">
          {/* {formatMoney(Number())} */}
          <MoneyFormattedOneTime callback={(_price) => `${isUSD ? currentWallet.realizedPnlUsd : currentWallet.realizedPnlSol / _price}`} showUnit isUSD={isUSD} unit={isUSD ? '$' : dataUnit} />
        </div>
      </div>

      <Analysis isLoading={loading} period={`${duration}D`} data={{
        balance: balanceSol,
        wallets: get(currentWallet, 'tokenHoldings', []),
        pnl: get(currentWallet, 'totalRealizedPnlUsd', '0'),
        avgPriceUsd: get(currentWallet, 'avgPriceUsd', '0'),
        realizedPnlUsd: get(currentWallet, 'realizedPnlUsd', '0'),
        buyAmountUsd: get(currentWallet, 'buyAmountUsd', '0'),
        totalCost: get(currentWallet, 'avgBuyAmountUsd', '0'),
        avgDuration: covertSeconds(currentWallet?.avgHoldDuration, 'd'),
        tokenAvgRealizedProfits: get(currentWallet, 'avgRealizedPnlUsd', '0'),
        txsBuy: get(currentWallet, 'buys', '0'),
        txsSell: get(currentWallet, 'sells', '0'),
        totalBuyUsd: get(currentWallet, 'totalBuyUsd', '0'),
      }} dataUnit={dataUnit} />
      <Distribution period={`${duration}D`} data={distribution} />
    </Fragment>
  )
}


const AssetsWalletDetailPage = () => {
  const { t } = useTranslation()
  const { address } = useParams()
  const [currentFilterTimeIndex, setCurrentFilterTimeIndex] = useState<number>(0)
  const [period, setPeriod] = useState<string>('1D')
  const [currentTab, setCurrentTab] = useState('Summary')
  const dispatch = useAppDispatch()
  const dataUnit = useAppSelector((state) => state.userSettings.dataUnit)
  const activeChain = useAppSelector((state) => state.wallet.activeChain)
  const tabs: UITab[] = [
    {
      value: 'Summary',
      label: t('walletDetail.summary'),
    },
    {
      value: 'Holdings',
      label: t('walletDetail.holdings.title'),
    },
    {
      value: 'Activity',
      label: t('walletDetail.activity.title'),
    },
  ]

  useEffect(() => {
    const selectedOption = filterTimeOptions[currentFilterTimeIndex]
    if (selectedOption) {
      setPeriod(`${selectedOption.value}${selectedOption.unit}`)
    }
  }, [currentFilterTimeIndex])

  const renderTabContent = (tab: string) => {
    switch (tab) {
      case 'Summary':
        return <HoldingTable address={address!} dataUnit={dataUnit} isOnlyHolding={false} />
      case 'Holdings':
        return <HoldingTable address={address!} dataUnit={dataUnit} isOnlyHolding={true} />
      case 'Activity':
        return <ActivityTable address={address!} dataUnit={dataUnit} />
      default:
        return null
    }
  }

  function handleChangeDataUnit() {
    if (dataUnit === 'USD') {
      dispatch(setDataUnit(activeChain === TYPE_CHAIN.SOLANA ? 'SOL' : activeChain === TYPE_CHAIN.ETH ? 'ETH' : 'USD'))
    } else {
      dispatch(setDataUnit('USD'))
    }
  }

  return (
    <div className="min-h-screen text-white">
      <HeaderWithBack
        title={''}
        className="justify-center bg-transparent top-0 left-0 right-0 max-w-[768px] mx-auto"
        titleClassName="ml-0"
        right={<a href='#' className="">
          <img src="/images/detailHeader/icon-share.svg" alt="" />
        </a>}
      />
      <div className="px-2.5 max-h-[calc(_100vh_-_48px)] overflow-x-hidden overflow-y-auto no-scrollbar">
        {address ? <WalletInfo address={address} /> : null}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center gap-0.5 flex-1 pr-2">
            <div className="text-[15px] text-white flex">{t('walletDetail.analysis.pnl', { period: period })}</div>
            <img
              src="/images/icons/fund-icon.svg"
              alt=""
              className="w-[16px] h-[16px] cursor-pointer"
              onClick={() => {
                if (dataUnit === 'USD') {
                  // setDataUnit(
                  //   activeChain === TYPE_CHAIN.SOLANA ? 'SOL' : activeChain === TYPE_CHAIN.ETH ? 'ETH' : 'USD',
                  // )
                  setDataUnit('SOL')
                } else {
                  setDataUnit('USD')
                }
              }}
            />
            <span className="text-[12px] text-white/70">{dataUnit}</span>
          </div>
          <FilterTime
            options={filterTimeOptions}
            defaultSelectedIndex={currentFilterTimeIndex}
            onChange={setCurrentFilterTimeIndex}
          />
        </div>
        {address ? <WalletStatistics address={address} duration={parseInt(filterTimeOptions[currentFilterTimeIndex].value)} dataUnit={dataUnit} /> : null}
        <div className="mt-7 relative pt-3">
          <MovingLineTabs
            tabs={tabs}
            disabledTabs={[]}
            defaultTab={currentTab}
            onTabChange={(tab) => setCurrentTab(tab)}
            containerClassName="justify-start bg-transparent"
          />
          <div
            className="absolute top-4 right-0 px-[6px] py-1 bg-[#ECECED14] rounded-[200px] flex flex-row items-center gap-1 cursor-pointer hover:bg-[#ECECED1A] transition-all duration-200 ease-in-out"
            onClick={() => {
              if (dataUnit === 'USD') {
                // setDataUnit(activeChain === TYPE_CHAIN.SOLANA ? 'SOL' : activeChain === TYPE_CHAIN.ETH ? 'ETH' : 'USD')
                setDataUnit('SOL')
              } else {
                setDataUnit('USD')
              }
            }}
          >
            <span className="text-[12px] font-[400] uppercase">{dataUnit}</span>
            <img src="/images/icons/fund-icon.svg" alt="" className="w-[16px] h-[16px]" />
          </div>
        </div>
        <div className="mt-2 pb-10">{renderTabContent(currentTab)}</div>
      </div>
    </div>
  )
}

export default AssetsWalletDetailPage
