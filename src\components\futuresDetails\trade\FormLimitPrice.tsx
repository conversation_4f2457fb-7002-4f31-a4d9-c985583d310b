import { coinOptionsSelector,symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice.ts'
import { RootState, useAppSelector } from '@/redux/store.ts'
import { UITab } from '@/types/uiTabs'
import SliderGradient from '@components/orderForm/SliderGradient.tsx'
import { useRef, useMemo } from 'react'
import useHandleChangeValue from '../hooks/useHandleChangeValue.tsx'
import CalcOrderInfo from './CalcOrderInfo.tsx'
import GroupOption from './GroupOption.tsx'
import InputBorderGradient from './InputBorderGradient.tsx'
import InputControl from './InputControl.tsx'
import { OrderContractState } from './type.order.ts'
interface FormLimitPriceProps {
  calcInfo: UITab[]
}

const FormLimitPrice = ({ calcInfo }: FormLimitPriceProps) => {
  const usdRef = useRef<HTMLInputElement>(null)
   const { lastTradePrice} = useAppSelector(symbolInfoSelector)
  const {
    orderInfo: { size, price, currency, sliderValue },
  } = useAppSelector<RootState, OrderContractState>((state) => state.orderContract)
  const coinOptions = useAppSelector(coinOptionsSelector)

  const { handleSizeChange, onSliderValueChange, handleOrderInfoChange } = useHandleChangeValue()

  /* 计算百分比 公式：（输入框的数值-最新价格）/最新价格*100%（四舍五入保留两位小数）*/
  // price 是输入框的数值,lastTradePrice 是最新成交价格
  const percentageValue = useMemo(() => {
    // 确保 price 和 lastTradePrice 都是有效数字
    const currentPrice = parseFloat(price?.toString() || '0')
    const lastPrice = parseFloat(lastTradePrice?.toString() || '0')
    // 如果任一价格无效或为0，返回0
    if (!currentPrice || !lastPrice || lastPrice === 0) {
      return 0
    }
    const percentage = ((currentPrice - lastPrice) / lastPrice) * 100
    return Math.round(percentage * 100) / 100
  }, [price, lastTradePrice])



  return (
    <div>
      <InputControl
        inputClassName='w-full'
        placeholder="价格"
        value={`${price ? price : ''}`}
        onChange={(e) => handleOrderInfoChange('price', e)}
        inputProps={{
          type: 'text',
          inputMode: 'decimal',
        }}
        colorBg="bg-[#ECECED1F]"
        onMinus={() => handleOrderInfoChange('price', Number(price) - 1)}
        onPlus={() => handleOrderInfoChange('price', Number(price) + 1)}
        formatThousands={true}
        showPercentageTooltip={true}
        percentageValue={percentageValue}
      />
      <InputBorderGradient
        unit={
          // <DrawerCheckSelect
          <div
            className="text-[#FFFFFF] cursor-pointer"
            onClick={() => {
              handleOrderInfoChange(
                'currency',
                currency === coinOptions[0].value ? coinOptions[1].value : coinOptions[0].value,
              )
            }}
          >
            <div className="flex items-center">
              <span>{currency}</span>
              <img className="ml-1" src="/images/futuresDetail/arrow-swap-icon.svg" />
            </div>
          </div>
          // childrenTrigger={
          // }
          // options={coinOptions}
          // value={currency}
          // onChange={(e) => handleOrderInfoChange('currency', e)}
          // />
        }
        placeholder="数量"
        value={`${size || ''}`}
        onChange={handleSizeChange}
        innerBgClassName="!bg-[#ECECED1F]"
        containerClassName="mb-[16px] w-full"
        inputClassName="flex-1"
        inputProps={{
          ref: usdRef,
          inputMode: 'decimal',
        }}
        formatThousands={true}
      />
      <SliderGradient
        containerClassName="mb-[30px]"
        sliderValue={sliderValue}
        onSliderValueChange={onSliderValueChange}
      />

      <GroupOption />

      <CalcOrderInfo calcList={calcInfo} />
    </div>
  )
}

export default FormLimitPrice
