import { IPOrderForm } from './OrderForm'
import useCustomToast from '@/hooks/useCustomToast'
import usePlaceAnOrder from '../hooks/usePlaceAnOrder'
import { useEffect, useMemo, useState } from 'react'
import ToastCenterScreen from '../ToastCenterScreen'
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle } from '@/components/ui/drawer'
import ButtonGradient from '@/components/common/buttons/ButtonGradient'
import CheckboxWithLabel from '@/components/common/CheckboxWithLabel'
import { Button } from '@/components/ui/button'
import { OrderInfo, OrderSide, OrderTypeEnum } from './type.order'
import { useAppSelector, useAppDispatch } from '@/redux/store'
import { futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import { isAuthorizedSelector, isDepositSelector, futuresUserInfoActions } from '@/redux/modules/futuresUserInfo.slice'
import { useIsLoggedInOnArb } from '@/hooks/hyperliquid/useIsLoggedInOnArb'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { checkHyperLiquidWallet } from '@services/auth.service.ts'
import ButtonApproveAgent from './ButtonApproveAgent'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { _changeTokenAccount } from '@/redux/modules/auth.slice'
import { useNavigate } from 'react-router-dom'
import { getClearinghouseState } from '@/api/hyperliquid'
import DepositDrawer from './DepositDrawer'
import ls from '@/lib/local-storage'
import { toast } from 'sonner'
import { symbolInfoSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { validateTpSlPrices } from '@/components/futuresDetails/helper/validateFunc'
import ButtonGreen from '@/components/common/buttons/ButtonGreen'
import { cn } from '@/lib/utils'
import { loadAgentWallet, saveAgentWallet } from '@/utils/agent/indexedDbUtils'
import { initAgentWalletIfNeeded, getAgentWallet } from '@/utils/agent/agentWalletManager'



export interface ConfirmPlaceOrderProps extends IPOrderForm {
  orderInfo: OrderInfo
  orderSide: OrderSide
  price: string
  liqPrice: string
  available: number
  orderMargin: number
  orderValue: number
}

const ConfirmPlaceOrder = ({ orderInfo, orderSide, baseCoin, price,
  liqPrice, available, orderMargin, orderValue }: ConfirmPlaceOrderProps) => {
  const dispatch = useAppDispatch()

  const isLogin = useIsLoggedInOnArb()

  const navigate = useNavigate()

  const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))

  const { activeWallet, activeChain } = useMultiChainWallet({})

  const [openDepositDrawer, setOpenDepositDrawer] = useState(false)

  const isApproveAgent = useAppSelector(isAuthorizedSelector)
  const isDeposit = useAppSelector(isDepositSelector)
  const leverage = Number(tradeConfigs.leverage)

  const { showToast, dismissToast } = useCustomToast()
  const {
    orderContract: { mutateAsync: placeOrder, isPending },
    handleSetPayload,
    twapOrderHandling,
    scaleOrderHandling,
  } = usePlaceAnOrder()

  const orderType = orderInfo.type

  const [showPrompt, setshowPrompt] = useState(false)
  const [open, setOpen] = useState(false)

  const [approveInfo, setApproveInfo] = useState({
    feeBuilderAddress: '',
    feeBuilderPercent: '',
    agentName: 'XBit',
    agentAddress: '',
  })

  const buttonLabel = orderSide === OrderSide.buy ? '买入/做多' : '卖出/做空'
  const orderSideDisplay = orderSide === OrderSide.buy ? '买入/做多' : '卖出/做空'
  const orderSideClass = orderSide === OrderSide.buy ? 'text-rise' : 'text-fall'

  const isEnoughMargin = available > orderMargin


  const buttonStatus = useMemo(() => {
    if (!activeWallet?.isConnected) {
      return 'connect'
    } else if (activeWallet?.isConnected && activeChain !== 'arb') {
      return 'toggle'
    } else if (!isLogin) {
      return 'login'
    } else if (isLogin && !isDeposit) {
      return 'deposit'
    } else if (isLogin && isDeposit && !isApproveAgent) {
      return 'approve'
    } else {
      return 'order'
    }


  }, [activeWallet, activeChain, isLogin, isApproveAgent])


  const orderBtnLabel = useMemo(() => {
    return isEnoughMargin ? '确认下单' : '保证金不足'
  }, [isEnoughMargin])




  const isVaildPlaceOrder = () => {
    if (orderValue <= 10) {
      toast.error('订单价值要大于10 USD')
      return false
    }
    const symbolPrice = price
    const { isShowTPSl, side, type, tpPrice, slPrice } = orderInfo
    if (isShowTPSl && type === OrderTypeEnum.market) {
      const resultValidateTpSlPrices = validateTpSlPrices({
        side: side,
        marketPrice: Number(symbolPrice),
        takeProfit: tpPrice,
        stopLoss: slPrice,
      })
      if (!resultValidateTpSlPrices.isValid) {
        toast.error(resultValidateTpSlPrices.message)
        return false
      }
    }
    return true

  }


  const handleConfirmOrder = async () => {
    ls.set('futures_hidden_order_confirm_modal', showPrompt)

    if (!isVaildPlaceOrder()) return

    try {
      if (orderType === OrderTypeEnum.twap) {
        const twapPayload = twapOrderHandling({ orderInfo, orderSide })
        await placeOrder(twapPayload)
      } else if (orderType === OrderTypeEnum.phased) {
        const scalePayload = scaleOrderHandling({ orderInfo, orderSide })
        await placeOrder(scalePayload)
      } else {
        // Standard Order Handling
        const payload = handleSetPayload({
          baseCoin,
          orderInfo,
          orderSide,
          price,
        } as any)


        if ('orders' in payload) {
          await placeOrder({
            ...payload,
            grouping: 'normalTpsl' as const,
          })
        } else {
          await placeOrder(payload)
        }
      }

      setOpen(false)
    } catch (error) {
      console.error('error:', error)
    }
  }

  const checkApproveAgent = async () => {
    const { data } = await userGqlClient.mutate({
      mutation: checkHyperLiquidWallet,
    })
    if (data?.checkHyperLiquidWallet) {
      const { feeBuilderAddress, feeBuilderPercent, agentName, agent } = data.checkHyperLiquidWallet
      const newFeeBuilderPercent = feeBuilderAddress.toLowerCase()
      setApproveInfo(prev => ({
        ...prev,
        feeBuilderAddress: newFeeBuilderPercent,
        feeBuilderPercent: `${feeBuilderPercent}%`,
    }))
      // To do
      const f = feeBuilderPercent / 100 * 1000
      dispatch(
        futuresUserInfoActions.updateBuilderInfo({
          b: newFeeBuilderPercent,
          f: f
        })
      )
      const isExpired =  Date.now() > (ls.get(`${activeWallet.walletId}_approve_expired_ts`) || 0)

      let isExistAgent = await loadAgentWallet(activeWallet.walletId)
      if (!isExistAgent || isExpired) {
        dispatch(
          futuresUserInfoActions.updateAuthorizationStatus(false)
        )
        await initAgentWalletIfNeeded(activeWallet.walletId)
        
      } else {
        dispatch(
          futuresUserInfoActions.updateAuthorizationStatus(true)
        )
      }

        const wallet = await getAgentWallet(activeWallet.walletId)
        dispatch(
          futuresUserInfoActions.updateAgentWallet({
            address: wallet.address,
            privateKey: wallet.privateKey
          })
        )
        setApproveInfo(prev => ({
            ...prev,
            agentAddress: wallet.address
        }))


    }
  }




  const checkDeposit = async () => {
    try {
      const data = await getClearinghouseState(activeWallet.walletId)
      const balance = parseFloat(data?.crossMarginSummary?.accountValue)
      // To do
      /*  dispatch(
           futuresUserInfoActions.updateDepositStatus(false)
       ) */
      dispatch(
        futuresUserInfoActions.updateDepositStatus(balance >= 10)
      )

    } catch (err: any) {

    }
  }

  const handleDisablePlaceOrderButton = () => {
    // need login
    if (!isLogin || isPending) return true
    if (orderType === OrderTypeEnum.market && !orderInfo.size) {
      return true
    } else if (orderType !== OrderTypeEnum.market) {
      if (!orderInfo.size || !orderInfo.price) {
        return true
      }
      if (!orderInfo.triggerPrice && orderType === OrderTypeEnum.tpsl) {
        return true
      }
    }
    if (!isEnoughMargin) return true
    return false
  }



  const handleClickOrderBtn = () => {
    if (!isVaildPlaceOrder()) return

    const isHiddenConfirmModal = ls.get('futures_hidden_order_confirm_modal')
    if (isHiddenConfirmModal) {
      handleConfirmOrder()
    } else {
      setOpen(true)
    }
  }


  useEffect(() => {
    if (isPending) {
      showToast(`${baseCoin}USD永续: 订单处理中`)
    } else {
      
      dismissToast()
    }
  }, [isPending])


  useEffect(() => {
    if (isLogin && activeWallet?.walletId) {
      checkApproveAgent()
      checkDeposit()
    }
  }, [isLogin, activeWallet?.walletId])


  useEffect(() => {
    dispatch(futuresUserInfoActions.updateOrderButtonStatus(buttonStatus))
  }, [buttonStatus])








  return (
    <>

      <DepositDrawer open={openDepositDrawer} setOpenDrawer={setOpenDepositDrawer} />

      <Drawer open={open} onOpenChange={setOpen}>

        {buttonStatus === 'connect' &&
          <ButtonGreen
            className={cn(
              'rounded-full w-full hover-scale',
              orderSide === OrderSide.buy ? 'green-gradient' : 'purple-gradient',
            )}
            onClick={() => {
              navigate(`/futures/discover`)
            }}
          >
            连接钱包
          </ButtonGreen>
        }

        {buttonStatus === 'toggle' &&
          <ButtonGreen
            className={cn(
              'rounded-full w-full hover-scale',
              orderSide === OrderSide.buy ? 'green-gradient' : 'purple-gradient',
            )}
            onClick={() => {
              navigate(`/futures/discover`)
            }}
          >
            切换钱包
          </ButtonGreen>
        }



        {buttonStatus === 'login' &&
          <ButtonGreen
            className={cn(
              'rounded-full w-full hover-scale',
              orderSide === OrderSide.buy ? 'green-gradient' : 'purple-gradient',
            )}
            onClick={() => {
              navigate(`/futures/discover`)
            }}
          >
            签名登录
          </ButtonGreen>
        }


        {buttonStatus === 'deposit' &&
          <ButtonGreen
            className={cn(
              'rounded-full w-full hover-scale',
              orderSide === OrderSide.buy ? 'green-gradient' : 'purple-gradient',
            )}
            onClick={() => {
              setOpenDepositDrawer(true)
            }}
          >
            请充值
          </ButtonGreen>
        }

        {buttonStatus === 'approve' &&
          <ButtonApproveAgent approveInfo={approveInfo} walletAddress={activeWallet.walletId}/>
        }

        {buttonStatus === 'order' &&
          <ButtonGreen
            onClick={() => { handleClickOrderBtn() }}
            className={cn(
              'rounded-full w-full hover-scale',
              orderSide === OrderSide.buy ? 'green-gradient' : 'purple-br-gradient',
            )}
            disabled={handleDisablePlaceOrderButton()}
            isLoading={isPending}
          >
            {orderBtnLabel}
          </ButtonGreen>
        }




        <DrawerContent className="min-h-[258px] w-full bg-[#232329] max-w-[768px] mx-auto">
          <DrawerHeader className="py-5 px-3.5 flex w-full items-center justify-between">
            <DrawerTitle className="flex items-center">
              <div className="text-[calc(1rem*(18/16))] leading-[calc(1rem*(18/16))]">确认订单</div>
            </DrawerTitle>
            <img
              src="/images/icons/icon-x.svg"
              className="w-6 h-6 cursor-pointer"
              onClick={() => setOpen(false)}
              alt=""
            />
          </DrawerHeader>
          <div className="px-3 pb-8 mt-6">
            <div className="mb-4">
              <div className="px-3 py-4 flex items-center justify-between bg-[#ECECED14] mb-2 rounded-[8px]">
                <span className="text-[#FFFFFFCC] text-[calc(14rem/16)] leading-[calc(14rem/16)]">方向</span>
                <div className="flex items-center">
                  <p className='text-rise bg-[#00FFB41A] rounded-[220px] px-2 py-1 text-[calc(12rem/16)] leading-[calc(12rem/16)] mr-2'>{`${leverage}x`}</p>
                  <p className={`${orderSideClass} text-[calc(15rem/16)] leading-[calc(15rem/16)]`}>{orderSideDisplay}</p>
                </div>
              </div>

              <p className="px-3 py-4 flex items-center justify-between bg-[#ECECED14] mb-2 rounded-[8px]">
                <span className="text-[#FFFFFFCC] text-[calc(14rem/16)] leading-[calc(14rem/16)]">数量</span>
                <span className="text-[#FFFFFF] text-[calc(15rem/16)] leading-[calc(15rem/16)]">
                  {orderInfo.size}
                  {orderInfo.currency}
                </span>
              </p>

              {orderType !== OrderTypeEnum.twap && (
                <p className="px-3 py-4 flex items-center justify-between bg-[#ECECED14] mb-2 rounded-[8px]">
                  <span className="text-[#FFFFFFCC] text-[calc(14rem/16)] leading-[calc(14rem/16)]">委托价格</span>
                  <span className="text-[#FFFFFF] text-[calc(15rem/16)] leading-[calc(15rem/16)]">
                    {orderType == OrderTypeEnum.market ? '市价' : orderInfo.price}
                  </span>
                </p>
              )}

              <p className="px-3 py-4 flex items-center justify-between bg-[#ECECED14] mb-2 rounded-[8px]">
                <span className="text-[#FFFFFFCC] text-[calc(14rem/16)] leading-[calc(14rem/16)]">订单价值</span>
                <span className="text-[#FFFFFF] text-[calc(15rem/16)] leading-[calc(15rem/16)]">
                  {orderValue.toFixed(2)}
                </span>
              </p>

              <p className="px-3 py-4 flex items-center justify-between bg-[#ECECED14] mb-2 rounded-[8px]">
                <span className="text-[#FFFFFFCC] text-[calc(14rem/16)] leading-[calc(14rem/16)]">所需保证金</span>
                <span className="text-[#FFFFFF] text-[calc(15rem/16)] leading-[calc(15rem/16)]">
                  {orderMargin.toFixed(2)}
                </span>
              </p>

              {/* <p className="px-3 py-4 flex items-center justify-between bg-[#ECECED14] mb-2 rounded-[8px]">
                <span className="text-[#FFFFFFCC] text-[calc(14rem/16)] leading-[calc(14rem/16)]">
                  {orderType !== OrderTypeEnum.twap ? '预估强平价' : '运行时长'}
                </span>
                <span className="text-[#FFFFFF] text-[calc(15rem/16)] leading-[calc(15rem/16)]">
                  {orderType !== OrderTypeEnum.twap ? liqPrice : '运行时长'}
                </span>
              </p> */}
            </div>

            <CheckboxWithLabel
              label={'不再提示'}
              defaultChecked={showPrompt}
              onChange={() => setshowPrompt(!showPrompt)}
            />
            <div className="grid grid-cols-2 gap-2 mt-5">
              <Button
                variant="borderGradient"
                className="text-(--text-primary) w-full rounded-[50px] h-[calc(1rem*(44/16))]"
                onClick={() => setOpen(false)}
              >
                取消
              </Button>
              <Button
                variant="gradient"
                className=" text-tertiary w-full rounded-[50px] h-[calc(1rem*(44/16))]"
                onClick={() => handleConfirmOrder()}
                isLoading={isPending}
                disabled={isPending}
              >
                {buttonLabel}
              </Button>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </>
  )
}
export default ConfirmPlaceOrder
