import { <PERSON>er, <PERSON>er<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, Drawer<PERSON>rigger } from '@/components/ui/drawer'
import { useState, useEffect, } from 'react'
import ButtonGradient from '@/components/common/buttons/ButtonGradient'
import ButtonShadowGradient from '@/components/common/buttons/ButtonShadowGradient'
import { updateHyperLiquidWalletMutation } from '@services/auth.service.ts'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { toast } from 'sonner'
import { futuresUserInfoActions } from '@/redux/modules/futuresUserInfo.slice'
import { useAppDispatch } from '@/redux/store'
import { signRequest, getSigner } from '@/utils/hyperliquidSign'
import { Configs } from '@/const/configs'
import ButtonGreen from '@/components/common/buttons/ButtonGreen'
import ls from '@/lib/local-storage'



interface ButtonApproveAgent {
  approveInfo: {
    feeBuilderAddress: string
    feeBuilderPercent: string
    agentAddress: string,
    agentName: string
  }
  walletAddress: string
}


const ButtonApproveAgent = (
  { approveInfo, walletAddress }: ButtonApproveAgent
) => {
  console.log('approveInfo', approveInfo)
  const dispatch = useAppDispatch()
  const [open, setOpen] = useState(false)


  const updateApproveAgentStastus = async () => {
    // const { feeBuilderAddress, feeBuilderPercent } = approveInfo
    const now = Date.now();
    // hyperqliuid 默认过期时间是90天
    const sixtyDaysLater = now + 90 * 24 * 60 * 60 * 1000;
    let status = true

    toast.success('授权成功')
    status = true
    ls.set(`${walletAddress}_approve_expired_ts`, sixtyDaysLater)


   /*  const percent = parseFloat(feeBuilderPercent.replace('%', ''))
    try {
      const { data } = await userGqlClient.mutate({
        mutation: updateHyperLiquidWalletMutation,
        variables: {
          input: {
            agentExpiredAt: sixtyDaysLater,
            setReferral: false,
            setFeeBuilder: true,
            feeBuilderAddress: feeBuilderAddress,
            feeBuilderPercent: percent,
            referralCode: ""
          }
        }
      })
      if (data?.updateHyperLiquidWallet?.approvedAgent) {
        toast.success('授权成功')
        status = true
        ls.set('approve_expired_ts', sixtyDaysLater)

      } else {
        toast.error('授权失败')
        status = false
      }
    } catch (error) {
      toast.error('授权失败')
      status = false
    }
 */
    dispatch(
      futuresUserInfoActions.updateAuthorizationStatus(status)
    )
  }

  const signatureApproveAgent = async () => {
    const { agentAddress, agentName } = approveInfo
    try {
      const signer = await getSigner()

      const signedRequest = await signRequest(signer, 'approveAgent', {
        agentAddress: agentAddress,
        agentName: agentName
      })

      const response = await fetch(`${Configs.getHyperliquidConfig().apiUrl}/exchange`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(signedRequest),
      })

      const result = await response.json()

      if (result.response === 'Extra agent already used.') {
        return {
          status: true
        }
      }

      if (result.status === 'err') {
        return {
          status: false,
          info: result.response
        }
      }

      return {
        status: true
      }


    } catch (error) {
      return {
        status: false,
        info: error
      }
    }
  }


  const signatureSetBuilderFee = async () => {
    const { feeBuilderPercent, feeBuilderAddress } = approveInfo
    try {
      const signer = await getSigner()

      const signedRequest = await signRequest(signer, 'approveBuilderFee', {
        builder: feeBuilderAddress,
        maxFeeRate: feeBuilderPercent
      })


      const response = await fetch(`${Configs.getHyperliquidConfig().apiUrl}/exchange`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(signedRequest),
      })

      const result = await response.json()


      if (result.status === 'err') {
        return {
          status: false,
          info: result.response
        }
      }

      return {
        status: true,
      }


    } catch (error) {
      return {
        status: false,
        info: error
      }
    }
  }


  const handleApproveAgent = async () => {
    const signatureApproveAgentResult = await signatureApproveAgent()
    if (!signatureApproveAgentResult.status) {
      signatureApproveAgentResult.info && toast.error(signatureApproveAgentResult.info)
      return
    }
    const signatureSetBuilderFeeResult = await signatureSetBuilderFee()

    if (!signatureSetBuilderFeeResult.status) {
      signatureSetBuilderFeeResult.info && toast.error(signatureSetBuilderFeeResult.info)
      return
    }


    updateApproveAgentStastus()
    setOpen(false)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>
        <ButtonGreen
          className="rounded-full w-full hover-scale"
        >
          授权交易
        </ButtonGreen>
      </DrawerTrigger>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto">
        <DrawerHeader className="py-5 px-3.5 flex w-full items-center justify-between">
          <DrawerTitle className="text-[calc(1rem*(18/16))] leading-[calc(1rem*(18/16))]">连接</DrawerTitle>
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt="close"
          />
        </DrawerHeader>

        <div className="px-3 pb-8">

          <div className="relative">
            <div className="mb-[45px] relative flex items-center">
              {/* <div className='bg-[url(/images/futuresDetail/approve-index-1-active.png)] bg-cover bg-center  size-8 mr-2'></div> */}
              <img className='size-8 mr-2' src="/images/futuresDetail/approve-index-1.png" alt='' />
              <div className='flex-1'>
                <p className='mb-1.5 text-[#FFFFFF] text-[calc(1rem*(16/16))] leading-[calc(1rem*(16/16))]'>
                  连接
                </p>
                <p className='text-[#FFFFFF80] text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]'>
                  将您的钱包连接到 XBIT。
                </p>
              </div>
            </div>

            <div className='mb-6 flex items-center'>
              <img className='size-8 mr-2' src="/images/futuresDetail/approve-index-2.png" alt='' />
              <div className='flex-1'>
                <p className='mb-1.5 text-[#FFFFFF] text-[calc(1rem*(16/16))] leading-[calc(1rem*(16/16))]'>
                  授权
                </p>
                <p className='text-[#FFFFFF80] text-[calc(1rem*(13/16))] leading-[calc(1rem*(13/16))]'>
                  授权并签署以继续此交易。
                </p>
              </div>

            </div>
          </div>

          <div className='mb-9 flex items-center text-[#FFFFFF] text-[calc(1rem*(11/16))] leading-[calc(1rem*(11/16))]
            bg-[#FF1D1D1A] p-2 rounded-[8px]'>
            <img src="/images/futuresDetail/danger-icon.svg" className='mr-1.5' alt='' />
            风险提示
          </div>


          <ButtonShadowGradient
            className="w-full rounded-[200px] h-11"
            onClick={() => { handleApproveAgent() }}
          >
            授权
          </ButtonShadowGradient>

        </div>
      </DrawerContent>
    </Drawer>
  )
}
export default ButtonApproveAgent