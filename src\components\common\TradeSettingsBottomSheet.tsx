import { useEffect, useState, useCallback, ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import { useAppSelector, useAppDispatch } from '@/redux/store'
import BottomSheet from '@components/common/BottomSheet.tsx'
import { ChainType } from '@/@generated/gql/graphql-trading'
import { TYPE_CHAIN } from '@/lib/blockchain'
import { Button } from '@components/ui/button.tsx'
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from '@components/ui/tooltip'
import { formatBalanceWallet } from '@/lib/number'
import useSubscription from '@/lib/mqtt/useSubscription'
import { useGetNetWorkFee } from '@/hooks/useGetNetWorkFee'
import { priceChain } from '@/redux/modules/price.slice'
import { BASIC_FEE } from '@/lib/blockchain'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  updateTradeSettings,
  resetTradeSettings,
  TradeSetting,
  setSelectedPreset,
} from '@/redux/modules/tradeSettings.slice'

type Props = {
  open: boolean
  setOpen: (open: boolean) => void
  type?: 1 | 2
}

const initialTradeSettings = {
  eth: [
    {
      key: 1,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'low',
          value: undefined,
        },
      },
      sell: {
        slippage: '20',
        mevProtect: true,
        fee: {
          type: 'low',
          value: undefined,
        },
      },
    },
    {
      key: 2,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'low',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'low',
          value: undefined,
        },
      },
    },
    {
      key: 3,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'low',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'low',
          value: undefined,
        },
      },
    },
  ],
  sol: [
    {
      key: 1,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
    },
    {
      key: 2,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
    },
    {
      key: 3,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
    },
  ],
  arb: [
    {
      key: 1,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
    },
    {
      key: 2,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
    },
    {
      key: 3,
      buy: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
      sell: {
        mevProtect: true,
        slippage: '20',
        fee: {
          type: 'medium',
          value: undefined,
        },
      },
    },
  ],
}

const TooltipWithInfo = ({ tooltipKey }: { tooltipKey: string }) => {
  const { t } = useTranslation()
  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger type="button">
          <img src="/images/icons/info.svg" alt="info" className="h-4 w-4 cursor-pointer" />
        </TooltipTrigger>
        <TooltipContent className="max-w-[360px]">
          <p className="text-xs leading-none">{t(tooltipKey)}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

const PresetSelector = ({
  presets,
  selectedKey,
  onSelect,
}: {
  presets: TradeSetting[]
  selectedKey: number
  onSelect: (preset: TradeSetting) => void
}) => {
  const { t } = useTranslation()
  return (
    <div className="flex gap-0.5 bg-[#2a2a2f] rounded-lg p-0.5 border-[0.5px] border-[#ECECED1F]">
      {presets.map((preset) => (
        <div
          key={preset.key}
          className={`flex-1 flex items-center justify-center py-1.5 rounded-sm cursor-pointer border-gradient style2 ${
            selectedKey === preset.key
              ? 'bg-gradient-to-tr from-[#E149F8]/10 via-[#9945FF]/10 to-[#00F3AB]/10'
              : 'before:invisible hover:before:visible'
          }`}
          onClick={() => onSelect(preset)}
        >
          <span
            className={`text-[14px] font-medium ${selectedKey === preset.key ? 'text-[#00FFB4]' : 'text-white/70'}`}
          >
            {t('tradeSettings.preset', { preset: preset.key })}
          </span>
        </div>
      ))}
    </div>
  )
}

const SideSelector = ({
  selected,
  onSelect,
}: {
  selected: 'buy' | 'sell'
  onSelect: (side: 'buy' | 'sell') => void
}) => {
  const { t } = useTranslation()
  return (
    <div className="mt-5 flex items-center gap-2">
      <div
        className={`flex-1 text-center cursor-pointer py-2 font-medium text-[15px] leading-none rounded-lg ${
          selected === 'buy'
            ? 'bg-gradient-to-r from-[#00E9A529] to-[#00FFB43D] text-[#00FFB4]'
            : 'bg-[#2f2f34] text-white/70'
        }`}
        onClick={() => onSelect('buy')}
      >
        {t('transaction.buy')}
      </div>
      <div
        className={`flex-1 text-center cursor-pointer py-2 font-medium text-[15px] leading-none rounded-lg ${
          selected === 'sell'
            ? 'bg-gradient-to-r from-[#AD1FFF1F] to-[#61009942] text-[#AB57FF]'
            : 'bg-[#2f2f34] text-white/70'
        }`}
        onClick={() => onSelect('sell')}
      >
        {t('transaction.sell')}
      </div>
    </div>
  )
}

const SlippageSelector = ({
  value,
  onChange,
  error,
}: {
  value: string
  onChange: (value: string) => void
  error?: string
}) => {
  const { t } = useTranslation()
  const slippageOptions = ['20', '10', '15', '50']

  return (
    <div className="mt-4">
      <div className="flex items-center gap-1">
        <span className="font-normal text-[14px] text-white/80 leading-none">{t('tradeSettings.slippage')}</span>
        <TooltipWithInfo tooltipKey="tradeSettings.slippageTooltip" />
      </div>
      <div className="mt-3 flex gap-2">
        {slippageOptions.map((option) => (
          <div
            key={option}
            className={`flex-1 flex items-center justify-center py-1.5 rounded-sm cursor-pointer border-gradient style2 ${
              value === option
                ? 'bg-gradient-to-tr from-[#E149F8]/10 via-[#9945FF]/10 to-[#00F3AB]/10'
                : 'bg-[#2a2a2f] rounded-lg border-[0.5px] border-[#ECECED1F] before:invisible hover:before:visible'
            }`}
            onClick={() => onChange(option)}
          >
            <span className={`text-[14px] font-normal ${value === option ? 'text-[#00FFB4]' : 'text-white/70'}`}>
              {option === '20' ? t('tradeSettings.auto') : option + '%'}
            </span>
          </div>
        ))}
      </div>
      <div className="mt-3 relative">
        <input
          className="w-full h-11 bg-[#111111] border border-[#ECECED1F] rounded-lg pl-3 pr-10 text-[14px] text-white/80 placeholder:text-white/50"
          placeholder={t('tradeSettings.custom') + ' (1 ~ 50)'}
          value={value}
          onChange={(e) => {
            let newValue = e.target.value.replace(/[^0-9.]/g, '')
            if (newValue.includes('.')) {
              const parts = newValue.split('.')
              newValue = parts[0] + '.' + parts[1]
            }
            onChange(newValue)
          }}
        />
        <span className="absolute top-1/2 right-3 -translate-y-1/2 text-[14px] text-white/70 font-[350]">%</span>
      </div>
      {error && <div className="mt-1 text-[14px] text-red-500 font-[350] leading-none">{error}</div>}
    </div>
  )
}

const FeeSelector = ({
  presetSelected,
  sideSelected,
  onChange,
  dataConfig,
  activeChain,
  priceSol,
  priceEth,
  priorityFeePriceKeyToLabel,
  isFeeError,
  setIsFeeError,
}: {
  presetSelected: TradeSetting
  sideSelected: 'buy' | 'sell'
  onChange: (type: string, value?: string) => void
  dataConfig: any
  activeChain: string
  priceSol: number
  priceEth: number
  priorityFeePriceKeyToLabel: (key: string) => string
  isFeeError: boolean
  setIsFeeError: (value: boolean) => void
}) => {
  const { t } = useTranslation()
  const getFeeSol = useCallback(
    (sol: number) => {
      if (sol)
        return formatBalanceWallet({
          balance: (sol * dataConfig?.maxComputeUnits) / Math.pow(10, 15),
          decimal: 6,
        })
      return 0
    },
    [dataConfig],
  )

  const getFeeEth = useCallback((eth: number) => {
    if (eth)
      return formatBalanceWallet({
        balance: eth,
        decimal: 3,
      })
    return 0
  }, [])

  if (activeChain === TYPE_CHAIN.SOLANA) {
    const minFee = getFeeSol(dataConfig?.priorityFeePrice?.medium) || 0
    const maxFee = 2
    return (
      <div className="mt-5">
        <div className="flex items-center gap-1">
          <span className="font-normal text-[14px] text-white/80 leading-none">{t('tradeSettings.priorityFee')}</span>
          <TooltipWithInfo tooltipKey="tradeSettings.priorityFeeTooltip" />
        </div>
        <div className="mt-3 flex gap-2">
          {dataConfig &&
            dataConfig.priorityFeePrice &&
            Object?.keys(dataConfig.priorityFeePrice).map((key) => {
              const fee = dataConfig.priorityFeePrice[key]
              return (
                <div
                  key={key}
                  className={`flex-1 flex items-center justify-center py-1.5 rounded-sm text-[12px] font-normal leading-none cursor-pointer border-gradient style2 ${
                    key === presetSelected[sideSelected].fee.type
                      ? 'bg-gradient-to-tr from-[#E149F8]/10 via-[#9945FF]/10 to-[#00F3AB]/10 text-[#00FFB4]'
                      : 'bg-[#2a2a2f] rounded-lg border-[0.5px] border-[#ECECED1F] text-white before:invisible hover:before:visible'
                  }`}
                  onClick={() => {
                    onChange(key)
                    setIsFeeError(false)
                  }}
                >
                  <div className="flex flex-col items-center gap-2 py-3">
                    <span>{priorityFeePriceKeyToLabel(key)}</span>
                    <span>{getFeeSol(fee)}SOL</span>
                    <div>
                      {formatBalanceWallet({
                        balance: +getFeeSol(fee) * priceSol,
                        decimal: 3,
                      }) !== '--'
                        ? '≈ $'
                        : ''}
                      {formatBalanceWallet({
                        balance: +getFeeSol(fee) * priceSol,
                        decimal: 3,
                      })}
                    </div>
                  </div>
                </div>
              )
            })}
        </div>
        <div className="mt-4 font-[350] text-[14px] text-white leading-none">
          {t('tradeSettings.custom') + ' ' + t('tradeSettings.priorityFee')}
          <span className="ml-1 text-white/65">({getFeeSol(dataConfig?.priorityFeePrice.medium)} ~ 2)</span>
        </div>
        <div className="mt-3 relative">
          <input
            className="w-full h-11 bg-[#111111] border border-[#ECECED1F] rounded-lg pl-3 pr-10 text-[14px] text-white/80 placeholder:text-white/50"
            placeholder={`${getFeeSol(dataConfig?.priorityFeePrice.medium)} ~ 2`}
            value={presetSelected[sideSelected].fee.value || ''}
            onChange={(e) => {
              let newValue = e.target.value.replace(/[^0-9.]/g, '')
              if (newValue.includes('.')) {
                const parts = newValue.split('.')
                newValue = parts[0] + '.' + parts[1]
              }
              const feeValue = Number(newValue)
              if (feeValue < Number(minFee) || feeValue > Number(maxFee) || isNaN(feeValue)) {
                setIsFeeError(true)
              } else {
                setIsFeeError(false)
              }
              onChange('custom', newValue)
            }}
          />
          <span className="absolute top-1/2 right-3 -translate-y-1/2 text-[14px] text-white/70 font-[350]">SOL</span>
        </div>
        {isFeeError && (
          <div className="mt-1 text-[14px] text-red-500 font-[350] leading-none">
            {t('tradeSettings.customPriorityFeeError', { min: minFee })}
          </div>
        )}
      </div>
    )
  }

  const gasFees = [
    {
      key: 'low',
      label: t('tradeSettings.marketPrice'),
    },
    {
      key: 'medium',
      label: t('tradeSettings.quick'),
    },
    {
      key: 'high',
      label: t('tradeSettings.fastest'),
    },
  ]

  if (activeChain === TYPE_CHAIN.ETH) {
    const minFee = getFeeEth(dataConfig?.low?.suggestedMaxFeePerGas) || 0
    return (
      <div className="mt-5">
        <div className="flex items-center gap-1">
          <span className="font-normal text-[14px] text-white/80 leading-none">{t('tradeSettings.gasFee')}</span>
          <TooltipWithInfo tooltipKey="tradeSettings.gasFeeTooltip" />
        </div>
        <div className="mt-3 flex gap-2">
          {dataConfig &&
            gasFees.map((fee) => (
              <div
                key={fee.key}
                className={`flex-1 flex items-center justify-center py-1.5 rounded-sm text-[12px] font-normal leading-none cursor-pointer border-gradient style2 ${
                  fee.key === presetSelected[sideSelected].fee.type
                    ? 'bg-gradient-to-tr from-[#E149F8]/10 via-[#9945FF]/10 to-[#00F3AB]/10 text-[#00FFB4]'
                    : 'bg-[#2a2a2f] rounded-lg border-[0.5px] border-[#ECECED1F] text-white before:invisible hover:before:visible'
                }`}
                onClick={() => {
                  onChange(fee.key)
                  setIsFeeError(false)
                }}
              >
                <div className="flex flex-col items-center gap-2 py-3">
                  <span>{fee.label}</span>
                  <span>{getFeeEth(dataConfig[fee.key]?.suggestedMaxFeePerGas)} Gwei</span>
                  <div>
                    ≈ $
                    {formatBalanceWallet({
                      balance: (dataConfig[fee.key]?.suggestedMaxFeePerGas * 200000 * priceEth) / Math.pow(10, 9),
                      decimal: 3,
                    })}
                  </div>
                </div>
              </div>
            ))}
        </div>
        <div className="mt-4 font-[350] text-[14px] text-white leading-none">
          {t('tradeSettings.custom') + ' ' + t('tradeSettings.gasFee')}
          <span className="ml-1 text-white/65">({getFeeEth(dataConfig?.low?.suggestedMaxFeePerGas)} ~ ∞)</span>
        </div>
        <div className="mt-3 relative">
          <input
            className="w-full h-11 bg-[#111111] border border-[#ECECED1F] rounded-lg pl-3 pr-10 text-[14px] text-white/80 placeholder:text-white/50"
            placeholder={`${getFeeEth(dataConfig?.low?.suggestedMaxFeePerGas)} ~ ∞`}
            value={presetSelected[sideSelected].fee.value || ''}
            onChange={(e) => {
              let newValue = e.target.value.replace(/[^0-9.]/g, '')
              if (newValue.includes('.')) {
                const parts = newValue.split('.')
                newValue = parts[0] + '.' + parts[1]
              }
              const feeValue = Number(newValue)
              if (feeValue < Number(minFee)) {
                setIsFeeError(true)
              } else {
                setIsFeeError(false)
              }
              onChange('custom', newValue)
            }}
          />
          <span className="absolute top-1/2 right-3 -translate-y-1/2 text-[14px] text-white/70 font-[350]">Gwei</span>
        </div>
        {isFeeError && (
          <div className="mt-1 text-[14px] text-red-500 font-[350] leading-none">
            {t('tradeSettings.customGasFeeError', { min: minFee })}
          </div>
        )}
      </div>
    )
  }
}

const CostSummary = ({
  activeChain,
  presetSelected,
  sideSelected,
  dataConfig,
  priceSol,
  priceEth,
}: {
  activeChain: string
  presetSelected: TradeSetting
  sideSelected: 'buy' | 'sell'
  dataConfig: any
  priceSol: number
  priceEth: number
}) => {
  const { t } = useTranslation()
  const getFeeSol = useCallback(
    (sol: number) => {
      if (sol)
        return formatBalanceWallet({
          balance: (sol * dataConfig?.maxComputeUnits) / Math.pow(10, 15),
          decimal: 6,
        })
      return 0
    },
    [dataConfig],
  )

  if (activeChain === TYPE_CHAIN.SOLANA) {
    return (
      <>
        <div className="mt-4 flex items-center justify-between font-[350] text-[14px] text-white leading-none">
          <div>{t('tradeSettings.totalCost')}:</div>
          <div>
            {formatBalanceWallet({
              balance:
                ((presetSelected[sideSelected]?.fee.value
                  ? Number(presetSelected[sideSelected].fee.value)
                  : Number(getFeeSol(dataConfig?.priorityFeePrice[presetSelected[sideSelected].fee.type]) || 0)) +
                  BASIC_FEE) *
                priceSol,
              decimal: 3,
            }) !== '--'
              ? '≈ $'
              : ''}
            {formatBalanceWallet({
              balance:
                ((presetSelected[sideSelected].fee.value
                  ? Number(presetSelected[sideSelected].fee.value)
                  : Number(getFeeSol(dataConfig?.priorityFeePrice[presetSelected[sideSelected].fee.type]) || 0)) +
                  BASIC_FEE) *
                priceSol,
              decimal: 3,
            })}
          </div>
        </div>
        <div className="mt-4 flex items-center justify-between font-[350] text-[14px] text-white/70 leading-none">
          <div>{t('tradeSettings.basicFee')}:</div>
          <div>
            {formatBalanceWallet({
              balance: BASIC_FEE * priceSol,
              decimal: 3,
            }) !== '--'
              ? '≈ $'
              : ''}
            {formatBalanceWallet({
              balance: BASIC_FEE * priceSol,
              decimal: 3,
            })}{' '}
            ({BASIC_FEE} SOL)
          </div>
        </div>
        <div className="mt-4 flex items-center justify-between font-[350] text-[14px] text-white/70 leading-none">
          <div>{t('tradeSettings.priorityFee')}:</div>
          <div>
            {formatBalanceWallet({
              balance: presetSelected[sideSelected].fee.value
                ? Number(presetSelected[sideSelected].fee.value) * priceSol
                : Number(getFeeSol(dataConfig?.priorityFeePrice[presetSelected[sideSelected].fee.type])) * priceSol,
              decimal: 3,
            }) !== '--'
              ? '≈ $'
              : ''}
            {formatBalanceWallet({
              balance: presetSelected[sideSelected].fee.value
                ? Number(presetSelected[sideSelected].fee.value) * priceSol
                : Number(getFeeSol(dataConfig?.priorityFeePrice[presetSelected[sideSelected].fee.type])) * priceSol,
              decimal: 3,
            })}{' '}
            (
            {presetSelected[sideSelected].fee.value
              ? presetSelected[sideSelected].fee.value
              : getFeeSol(dataConfig?.priorityFeePrice[presetSelected[sideSelected].fee.type])}{' '}
            SOL)
          </div>
        </div>
      </>
    )
  }

  if (activeChain === TYPE_CHAIN.ETH) {
    return (
      <div className="mt-4 flex items-center justify-between font-[350] text-[14px] text-white leading-none">
        <div>{t('tradeSettings.totalCost')}:</div>
        <div>
          ≈ $
          {formatBalanceWallet({
            balance: presetSelected[sideSelected].fee.value
              ? (Number(presetSelected[sideSelected].fee.value) * priceEth * 200000) / Math.pow(10, 9)
              : (dataConfig[presetSelected[sideSelected].fee.type]?.suggestedMaxFeePerGas * priceEth * 200000) /
                Math.pow(10, 9),
            decimal: 3,
          })}
        </div>
      </div>
    )
  }
}

const TradeSettingsBottomSheet = ({ open, setOpen, type = 1 }: Props) => {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const activeChain = useAppSelector((state) => state.wallet.activeChain) as TYPE_CHAIN
  const [dataConfig, setDataConfig] = useState<any>()
  const tradeSettings = useAppSelector((state) => state.tradeSettings.settings) || initialTradeSettings
  const selectedPresetKey = useAppSelector((state) => state.tradeSettings.selectedPreset[activeChain])
  const chain = activeChain === TYPE_CHAIN?.ETH ? ChainType.Evm : ChainType.Solana
  const { data } = useGetNetWorkFee(activeChain)
  const priceSol = useAppSelector(priceChain('SOL'))
  const priceEth = useAppSelector(priceChain('ETH'))
  const [openDialogConfirm, setOpenDialogConfirm] = useState(false)

  const [tradeSettingsByChain, setTradeSettingsByChain] = useState<TradeSetting[]>(initialTradeSettings[activeChain])
  const [presetSelected, setPresetSelected] = useState<TradeSetting>(tradeSettingsByChain[0])
  const [sideSelected, setSideSelected] = useState<'buy' | 'sell'>('buy')
  const [isFeeError, setIsFeeError] = useState(false)

  useEffect(() => {
    if (open) {
      setTradeSettingsByChain(tradeSettings[activeChain] || initialTradeSettings[activeChain])
    }
  }, [open])

  useEffect(() => {
    setTradeSettingsByChain(tradeSettings[activeChain] || initialTradeSettings[activeChain])
  }, [activeChain, tradeSettings])

  useEffect(() => {
    setSideSelected('buy')
  }, [selectedPresetKey])

  useEffect(() => {
    const newPresetSelected = tradeSettingsByChain.find((preset) => preset.key === selectedPresetKey)
    if (newPresetSelected) {
      setPresetSelected(newPresetSelected)
    }
  }, [tradeSettingsByChain, selectedPresetKey])

  useEffect(() => {
    if (data) {
      if (activeChain === TYPE_CHAIN.SOLANA) {
        setDataConfig(data?.getNetworkFee?.solana)
      }
      if (activeChain === TYPE_CHAIN.ETH) {
        setDataConfig(data?.getNetworkFee?.ethereum)
      }
    }
  }, [data, activeChain])

  const _message = useSubscription(`public/network_fee_updated/${chain}`)
  const message = _message?.message?.message

  useEffect(() => {
    if (!message) return
    try {
      const dataMqtt = JSON.parse(message.toString() || '')
      if (dataMqtt) {
        if (activeChain === TYPE_CHAIN.SOLANA) {
          setDataConfig(dataMqtt?.solana)
        }
        if (activeChain === TYPE_CHAIN.ETH) {
          setDataConfig(dataMqtt?.ethereum)
        }
      }
    } catch (error) {
      console.warn('[NetworkFeeSubscription error]: ', error)
    }
  }, [message, activeChain])

  const resetTradeSettingsHandler = useCallback(() => {
    dispatch(resetTradeSettings(activeChain))
    dispatch(updateTradeSettings({ chain: activeChain, settings: initialTradeSettings[activeChain] }))
    setPresetSelected(initialTradeSettings[activeChain][0])
    setSideSelected('buy')
  }, [dispatch, activeChain])

  const applyTradeSettings = useCallback(() => {
    dispatch(updateTradeSettings({ chain: activeChain, settings: tradeSettingsByChain }))
    setOpenDialogConfirm(false)
    setOpen(false)
  }, [dispatch, activeChain, tradeSettingsByChain, setOpen])

  const getActiveChainLogo = useCallback((chain: string) => {
    switch (chain) {
      case 'sol':
        return '/images/solana.webp'
      case 'eth':
        return '/images/ether.svg'
      case 'arb':
        return '/images/arbitrum.svg'
      default:
        return ''
    }
  }, [])

  const priorityFeePriceKeyToLabel = useCallback(
    (key: string) => {
      switch (key) {
        case 'medium':
          return t('tradeSettings.marketPrice')
        case 'high':
          return t('tradeSettings.quick')
        case 'veryHigh':
          return t('tradeSettings.fastest')
        default:
          return key
      }
    },
    [t],
  )

  const handleFeeChange = useCallback(
    (type: string, value?: string) => {
      setTradeSettingsByChain((prev) => {
        const updatedTradeSettings = prev.map((item) => {
          if (item.key === presetSelected.key) {
            return {
              ...item,
              [sideSelected]: {
                ...item[sideSelected],
                fee: {
                  type,
                  value,
                },
              },
            }
          }
          return item
        })
        return updatedTradeSettings
      })
    },
    [presetSelected.key, sideSelected],
  )

  const handleSlippageChange = useCallback(
    (value: string) => {
      setTradeSettingsByChain((prev) => {
        const updatedTradeSettings = prev.map((item) => {
          if (item.key === presetSelected.key) {
            return {
              ...item,
              [sideSelected]: {
                ...item[sideSelected],
                slippage: value,
              },
            }
          }
          return item
        })
        return updatedTradeSettings
      })
    },
    [presetSelected.key, sideSelected],
  )

  const handleMevProtectToggle = useCallback(() => {
    setTradeSettingsByChain((prev) => {
      const updatedTradeSettings = prev.map((item) => {
        if (item.key === presetSelected.key) {
          return {
            ...item,
            [sideSelected]: {
              ...item[sideSelected],
              mevProtect: !item[sideSelected].mevProtect,
            },
          }
        }
        return item
      })
      return updatedTradeSettings
    })
  }, [presetSelected.key, sideSelected])

  if (activeChain === TYPE_CHAIN.ARB) {
    // TODO: Implement ARB chain settings when available
    return <></>
  }

  return (
    <>
      {type === 2 ? (
        <div className="flex items-center justify-between border-t border-[#ECECED1F] pt-2">
          <div className="flex items-center gap-1">
            {tradeSettingsByChain.map((preset) => (
              <div
                key={preset.key}
                className={`flex items-center justify-center text-[12px] px-1 py-0.5 rounded-sm cursor-pointer hover:bg-[#2a2a2f] ${
                  presetSelected.key === preset.key
                    ? 'bg-[#2a2a2f] border-gradient style2 text-white'
                    : 'border-[0.5px] text-white/50'
                }`}
                onClick={() => {
                  setPresetSelected(preset)
                  dispatch(setSelectedPreset({ chain: activeChain, presetKey: preset.key }))
                }}
              >
                P{preset.key}
              </div>
            ))}
          </div>
          <TooltipProvider delayDuration={200}>
            <Tooltip>
              <TooltipTrigger type="button">
                <img
                  src="/images/icons/ic-settings.svg"
                  alt="settings"
                  className="w-[16px] h-[16px] rounded-full cursor-pointer hover:scale-110 transition-transform duration-200"
                  onClick={() => setOpen(true)}
                />
              </TooltipTrigger>
              <TooltipContent className="max-w-[360px]">
                <p className="text-xs leading-none">{t('tradeSettings.title')}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      ) : (
        <div className="flex items-center justify-center gap-1 cursor-pointer" onClick={() => setOpen(true)}>
          <img src={getActiveChainLogo(activeChain)} alt="chain-logo" className="w-[16px] h-[16px] rounded-full" />
          <span className="text-[12px] text-white/80 font-normal">P{presetSelected.key}</span>
        </div>
      )}
      <BottomSheet open={open} setOpen={setOpen} title={t('tradeSettings.title')}>
        <div className="overflow-y-auto no-scrollbar max-h-[calc(80vh-150px)]">
          <PresetSelector
            presets={tradeSettingsByChain}
            selectedKey={presetSelected.key}
            onSelect={(preset) => {
              setPresetSelected(preset)
              dispatch(setSelectedPreset({ chain: activeChain, presetKey: preset.key }))
            }}
          />
          <SideSelector selected={sideSelected} onSelect={setSideSelected} />
          <div className="mt-5 flex items-center justify-between">
            <div className="flex items-center gap-1">
              <span className="font-normal text-[14px] text-white/80 leading-none">
                {t('tradeSettings.antiClipMode')}
              </span>
              <TooltipWithInfo tooltipKey="tradeSettings.antiClipModeTooltip" />
            </div>
            <div
              className={`flex items-center w-10 h-[26px] rounded-full cursor-pointer ${
                presetSelected[sideSelected].mevProtect ? ' justify-end bg-[#00FFB4]' : 'justify-start bg-[#ECECED1F]'
              }`}
              onClick={handleMevProtectToggle}
            >
              <div className=" w-6 h-6 bg-white rounded-full transition-all duration-300 ease-in-out"></div>
            </div>
          </div>
          <SlippageSelector
            value={presetSelected[sideSelected].slippage}
            onChange={handleSlippageChange}
            error={
              presetSelected[sideSelected].slippage &&
              (Number(presetSelected[sideSelected].slippage) < 1 || Number(presetSelected[sideSelected].slippage) > 50)
                ? t('tradeSettings.customSlippageError')
                : undefined
            }
          />
          <FeeSelector
            presetSelected={presetSelected}
            sideSelected={sideSelected}
            onChange={handleFeeChange}
            dataConfig={dataConfig}
            activeChain={activeChain}
            priceSol={priceSol}
            priceEth={priceEth}
            priorityFeePriceKeyToLabel={priorityFeePriceKeyToLabel}
            isFeeError={isFeeError}
            setIsFeeError={setIsFeeError}
          />
          <CostSummary
            activeChain={activeChain}
            presetSelected={presetSelected}
            sideSelected={sideSelected}
            dataConfig={dataConfig}
            priceSol={priceSol}
            priceEth={priceEth}
          />

          <div className="mt-6 bg-[#ECECED0A] p-2 rounded-lg flex items-center gap-2">
            <img src="/images/icons/danger.svg" alt="danger" className="w-5 h-5" />
            <div className="font-[350] text-[11px] text-white/70 leading-[1.5]">{t('tradeSettings.danggerNote')}</div>
          </div>
        </div>
        <div className="w-full mt-3 pt-3 border-t border-[#ECECED0A] flex gap-4 items-center">
          <Button variant="borderGradient" className="rounded-full flex-1 h-11" onClick={resetTradeSettingsHandler}>
            {t('tradeSettings.reset')}
          </Button>
          <Button
            variant="gradient"
            className="rounded-full flex-1 h-11 text-black"
            onClick={() => setOpenDialogConfirm(true)}
            disabled={
              !presetSelected[sideSelected].slippage ||
              isNaN(Number(presetSelected[sideSelected].slippage)) ||
              Number(presetSelected[sideSelected].slippage) < 1 ||
              Number(presetSelected[sideSelected].slippage) > 50 ||
              isFeeError
            }
          >
            {t('tradeSettings.apply')}
          </Button>
        </div>
        <Dialog open={openDialogConfirm} onOpenChange={setOpenDialogConfirm}>
          <DialogContent className="w-[360px] bg-[#232329] rounded-2xl p-5">
            <DialogHeader>
              <DialogTitle>
                <p className="text-sm text-white leading-[1.5] pt-8">{t('orderForm.orderSetting.warning')}</p>
              </DialogTitle>
              <div className="flex justify-center items-center flex-row gap-2.5 mt-4">
                <Button type="button" variant="close" className="flex-1" onClick={() => setOpenDialogConfirm(false)}>
                  {t('button.cancel')}
                </Button>
                <Button
                  variant="gradient"
                  type="button"
                  className="text-[#261236] flex-1 rounded-[50px]"
                  onClick={applyTradeSettings}
                >
                  {t('button.confirm')}
                </Button>
              </div>
            </DialogHeader>
            <DialogDescription />
          </DialogContent>
        </Dialog>
      </BottomSheet>
    </>
  )
}

export default TradeSettingsBottomSheet
