import Text from '@/components/common/Text'
import { LeverageBadge, PriceChange } from '@/components/futuresDiscover/table/crypto-table'
import { TableVirtual } from '@/components/futuresDiscover/table/table-virtual'
import { IconSortDown, IconSortUp } from '@/components/icon'
import { formatMoney, formatNumberWithCommas, formatPercentage } from '@/utils/helpers'
import { createColumnHelper } from '@tanstack/react-table'
import React, { useCallback, useEffect, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import useHandleGetData, { useSearchFilter } from '../hooks/useHandleGetData'
import useSortableTable from '../hooks/useSortableTable'
import { ISymbolList } from '../type'

const SortHeader = React.memo(
  ({
    text,
    onSort,
    sortIndicator,
  }: {
    text: string
    onSort: () => void
    sortIndicator: { upColor: string; downColor: string }
  }) => (
    <div className="flex cursor-pointer" onClick={onSort}>
      <Text text={text} fontSize={11} fontWeight="light" color="#FFFFFF80" className="cursor-pointer" />
      <div className="flex flex-col ml-1 cursor-pointer">
        <IconSortUp currentColor={sortIndicator.upColor} />
        <IconSortDown currentColor={sortIndicator.downColor} />
      </div>
    </div>
  ),
)

SortHeader.displayName = 'SortHeader'

const VolumeList = ({ symbolData, search }: { symbolData: ISymbolList[]; search: string }) => {
  const columnHelper = createColumnHelper<ISymbolList>()
  const { isLoading, symbolList } = useHandleGetData({ condition: 'volume' })

  const currentData = useMemo(() => {
    if (!symbolList || symbolList.length === 0) {
      return []
    }

    if (!symbolData || symbolData.length === 0) {
      return symbolList
    }

    const symbolDataMap = new Map(symbolData.map((item) => [item.symbol, item]))

    return symbolList.map((apiItem) => {
      const subscriptionItem = symbolDataMap.get(apiItem.symbol)
      return subscriptionItem || apiItem
    })
  }, [symbolData, symbolList, search])

  const filteredData = useSearchFilter(currentData || [], search)

  const { sortedData, handleSort, getSortIndicator } = useSortableTable<ISymbolList>(filteredData)
  const navigate = useNavigate()


  const handleRowClick = useCallback(
    (row: ISymbolList) => {
      navigate(`/futures/${row.symbol}`)
    },
    [navigate],
  )

  const sortHandlers = useMemo(
    () => ({
      symbol: () => handleSort('symbol'),
      marketCap: () => handleSort('marketCap'),
      currentPrice: () => handleSort('currentPrice'),
      volume: () => handleSort('volume'),
      changPxPercent: () => handleSort('changPxPercent'),
    }),
    [handleSort],
  )

  const sortIndicators = useMemo(
    () => ({
      symbol: getSortIndicator('symbol'),
      marketCap: getSortIndicator('marketCap'),
      currentPrice: getSortIndicator('currentPrice'),
      volume: getSortIndicator('volume'),
      changPxPercent: getSortIndicator('changPxPercent'),
    }),
    [getSortIndicator],
  )

  const columns = useMemo(
    () => [
      columnHelper.accessor('symbol', {
        header: () => (
          <div className="flex items-center">
            <div className="flex items-center gap-1.5">
              <SortHeader text="币种" onSort={sortHandlers.symbol} sortIndicator={sortIndicators.symbol} />
              <SortHeader text="市值" onSort={sortHandlers.marketCap} sortIndicator={sortIndicators.marketCap} />
            </div>
          </div>
        ),
        cell: (info) => {
          const { symbol, maxLeverage, marketCap } = info.row.original

          return (
            <div className="flex items-center space-x-3">
              <div className="flex flex-col gap-1">
                <div className="flex items-end gap-1">
                  <Text text={symbol} fontSize={15} fontWeight="medium" className="leading-[calc(1rem*(15/16))]" />
                  <Text
                    text="/"
                    fontSize={9}
                    fontWeight="light"
                    color="#FFFFFF80"
                    className="leading-[calc(1rem*(12/16))]"
                  />
                  <Text
                    text={'USDC'}
                    fontSize={11}
                    fontWeight="light"
                    color="#FFFFFF80"
                    className="leading-[calc(1rem*(11/16))]"
                  />
                  <LeverageBadge value={maxLeverage as unknown as string} />
                </div>
                <div className="flex gap-1 items-end">
                  <div className="text-[calc(1rem*(12/16))] tex-[#FFFFFFB2] lining-nums">{formatMoney(marketCap)}</div>
                </div>
              </div>
            </div>
          )
        },
      }),

      columnHelper.accessor('currentPrice', {
        header: () => (
          <div className="flex items-center gap-2 justify-end">
            <div className="flex items-center gap-1.5">
              <SortHeader text="价格" onSort={sortHandlers.currentPrice} sortIndicator={sortIndicators.currentPrice} />
              <SortHeader text="成交额" onSort={sortHandlers.volume} sortIndicator={sortIndicators.volume} />
            </div>
          </div>
        ),
        cell: (info: any) => {
          const currentPrice = info.getValue()
          const volume = info.row.original.volume

          return (
            <div className="flex items-end gap-1 flex-col relative">
              <Text text={formatNumberWithCommas(`${currentPrice}`, 9)} fontSize={15} fontWeight="medium" className='lining-nums'  />
              <Text text={formatMoney(volume)} fontSize={11} fontWeight="regular" color="#FFFFFFB2" className='lining-nums' />
            </div>
          )
        },
      }),
      columnHelper.accessor('changPxPercent', {
        header: () => (
          <div className="flex justify-end gap-2 ">
            <div className="flex items-center text-right">
              <SortHeader
                text="涨跌幅"
                onSort={sortHandlers.changPxPercent}
                sortIndicator={sortIndicators.changPxPercent}
              />
            </div>
          </div>
        ),
        cell: (info) => {
          const changePercent = info.getValue()
          return <PriceChange value={formatPercentage(changePercent)} isPositive={Number(changePercent) > 0} />
        },
      }),
    ],
    [columnHelper, sortHandlers, sortIndicators],
  )

  return (
    <div className="">
      <TableVirtual<ISymbolList, any>
        columns={columns}
        data={sortedData}
        onRowClick={handleRowClick}
        isLoading={isLoading}
        isStickyHeader={false}
        containerClassName="!border-none _hidescrollbar"
        tableHeaderClassName="text-[#FFFFFF80] text-[calc(1rem*(12/16))] font-[400]"
        tableHeaderRowClassName="!border-none "
        tableCellClassName="group-hover:!bg-[#27272a] cursor-pointer !border-none !py-2.5 justify-end px-table-cell"
        tableHeadClassName="px-table-cell"
        tableRowClassName="!border-none"
      />
      
    </div>
  )
}

export default React.memo(VolumeList)
