import Container from '@components/common/Container.tsx'
import React, { useState, useEffect, useMemo, useCallback } from 'react'
import CurrentOrderCard from './CurrentOrderCard'
import CurrentOrderFilter from './CurrentOrderFilter'
import isEqual from 'lodash/isEqual';
import { xOpenOrders, OrderType, OrderSide, All } from '../types'
import { IconEmpty } from '@/components/icon'
import { toast } from 'sonner'
import { useCancelOrdersMutation } from '@/components/futuresDetails/hooks/useCancelOrdersMutation'
import { selectAllPerpMeta } from '@/redux/modules/futuresMeta.slice'
import { useAppSelector, useAppDispatch } from '@/redux/store'
import { agentWalletSelector } from '@/redux/modules/futuresUserInfo.slice'





export type OrdersListFilterOrderType = 'All' | OrderType

export type OrdersListFilter = {
  showOnlyBaseCoin: boolean
  type?: OrderType | All
  side?: OrderSide | All
}


interface CurrentOrdersListProps {
  orders: xOpenOrders[]
  baseCoin: string
}

const CurrentOrdersList = ({orders, baseCoin,}: CurrentOrdersListProps) => {
  const [filter, setFilter] = useState<OrdersListFilter>({
    showOnlyBaseCoin: false,
    type: 'All',
    side: 'All'
  })


  const allMeta = useAppSelector(selectAllPerpMeta)

  const agentWallet = useAppSelector(agentWalletSelector)

  const { mutate: cancelOrders, isPending } = useCancelOrdersMutation()


  const filterOrders = useMemo(() => {
    if (filter.type === 'All' && filter.side === 'All' && !filter.showOnlyBaseCoin) {
      return orders
    }

 
    
    return orders.filter(item => {
      return (item.orderType === filter.type || (filter.type === 'All') )
       && (item.side === filter.side || (filter.side === 'All') )
       && (!filter.showOnlyBaseCoin || (baseCoin === item.coin))
    })
  }, [filter, orders])

  const handleCancelOrders = async () => {
    if (!orders.length || !agentWallet) return;
    cancelOrders(
      { orders: orders, allMeta, agentWallet },
        {
          onSuccess: () => {
             toast('撤销全部委托成功')
          },
          onError: (err) => {
            toast('撤销全部委托失败')
          },
        }
    )
   
  } 


  



  return (
    <Container className="mt-[10px]">
      <CurrentOrderFilter 
        filter={filter} 
        setFilter={setFilter} 
        onClickCancelAll={()=> handleCancelOrders()} 
      />
      <div className="flex flex-col gap-2.5">
        {
          filterOrders.map(item => (
            <CurrentOrderCard 
              key={item.oid}
              orderInfo={item} 
            />
          ))
        }
        {filterOrders?.length === 0 && (
          <div className="flex flex-col items-center justify-center h-80">
            <IconEmpty/>
            <span className="text-[#FFFFFF80] text-[0.75rem]">没有数据</span>
          </div>
        )}
      </div>
    </Container>
  )
}

export default React.memo(CurrentOrdersList, (prevProps, nextProps) =>
  isEqual(prevProps.orders, nextProps.orders)
);
