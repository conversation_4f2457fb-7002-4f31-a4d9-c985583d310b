import { useEffect, useMemo, useState } from 'react'
import FilterSelect, { FilterSelectOption } from '@components/common/FilterSelect'
import FormOneClick from '@components/orderForm/FormOneClick.tsx'
import FormMarketPrice from '@components/orderForm/FormMarketPrice.tsx'
import { FormProvider, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { CreateOrderInput, Order, OrderType, TransactionType } from '@/@generated/gql/graphql-trading'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import FormLimitPrice from '@components/orderForm/FormLimitPrice.tsx'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { orderActions } from '@/redux/modules/order.slice'
import { toast } from 'sonner'
import ls from '@/lib/local-storage'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '../ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@components/ui/tooltip'
import { useQuery } from '@apollo/client'
import { TokenDetail } from '@/@generated/gql/graphql-core'
import FormTrailing from './FormTrailing'
import { gqlClient } from '@/lib/gql/apollo-client'
import isEmpty from 'lodash/isEmpty'
import {
  FRIST_FEE_SOL,
  MIN_BALANCE_SOL,
  TYPE_ACCOUNT,
  TYPE_CHAIN,
  PLATFORM_FEE_SOL,
  solanaConnection,
  BASIC_FEE,
} from '@/lib/blockchain'
import BigNumber from 'bignumber.js'
import { Configs } from '@/const/configs'
import { Buffer } from 'buffer'
import { useWallet } from '@solana/wallet-adapter-react'
import { PublicKey, PublicKeyInitData, VersionedTransaction } from '@solana/web3.js'
import { useTranslation } from 'react-i18next'
import CheckboxWithLabel from '@components/common/CheckboxWithLabel.tsx'
import { ServiceConfig } from '@/lib/gql/service-config'
import { cn } from '@/lib/utils.ts'
import { getPortfolio } from '@/services/tokens.service'
import useSignWallet from '@/hooks/useSignWallet'
import { SOL_ADDRESS } from '@/hooks/useCreateOrder'
import { getAssociatedTokenAddress, TOKEN_PROGRAM_ID } from '@solana/spl-token'
import useShowToastOrder from '@/hooks/useShowToastOrder'
import { ChainIds } from '@/types/enums'
import TradeSettingsBottomSheet from '../common/TradeSettingsBottomSheet'
import useNetworkFeeSubcription from '../mqtt/NetworkFeeSubcription'
import FormSwitchWallet from './FormDeposit'
import useWatchWalletTokenBalance from '@/hooks/useWatchWalletTokenBalance'

type DetailOrderType = 'oneClick' | 'marketPrice' | 'limitPrice' | 'trailingTpSl'

const formSchema = z.object({
  transactionType: z.string({
    required_error: 'required',
  }),
  type: z.string({
    required_error: 'required',
  }),
  baseAddress: z.string({
    required_error: 'required',
  }),
  quoteAddress: z.string({
    required_error: 'required',
  }),
  userAddress: z.string({
    required_error: 'required',
  }),
  chainId: z.string({
    required_error: 'required',
  }),
  quoteAmount: z.string({}),
  baseAmount: z.string({}),
  doublePrincipalAfterPurchase: z.boolean({}),
  tp: z.string({}),
  sl: z.string({}),
  limitPrice: z.string({}),
  limitMarketCap: z.string({}),
  callbackRate: z.string({}),
  triggerPrice: z.string({}),
})

export type FormValues = z.infer<typeof formSchema>

const OrderForm = ({ tokenDetail }: { tokenDetail: TokenDetail }) => {
  const { t } = useTranslation()

  const orderTypesOptions: FilterSelectOption[] = [
    {
      value: 'oneClick' as DetailOrderType,
      label: t('orderForm.tabs.quickTradeBuy'),
    },
    {
      value: 'marketPrice' as DetailOrderType,
      label: t('orderForm.tabs.marketTrade'),
    },
    {
      value: 'limitPrice' as DetailOrderType,
      label: t('orderForm.tabs.limitOrder'),
    },
  ]
  const orderSellOptions: FilterSelectOption[] = [
    {
      value: 'oneClick' as DetailOrderType,
      label: t('orderForm.tabs.quickTradeSell'),
    },
    {
      value: 'marketPrice' as DetailOrderType,
      label: t('orderForm.tabs.marketTrade'),
    },
    {
      value: 'limitPrice' as DetailOrderType,
      label: t('orderForm.tabs.limitOrder'),
    },
    {
      value: 'trailingTpSl' as DetailOrderType,
      label: t('orderForm.tabs.trailingStopLoss'),
    },
  ]

  const params = new URLSearchParams(window.location.search)
  const langUrl = params.get('lang')
  const lang =
    langUrl || localStorage.getItem('i18nextLng')?.substring(0, 2) || navigator.language?.substring(0, 2) || 'en'

  const [orderType, setOrderType] = useState<DetailOrderType>(orderTypesOptions[0].value as DetailOrderType)
  const { activeWallet } = useMultiChainWallet({})
  const dispatch = useAppDispatch()
  const { wallet, sendTransaction } = useWallet()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [openTradeSettings, setOpenTradeSettings] = useState(false)
  const activeChain = useAppSelector((state) => state.wallet.activeChain)
  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const selectedPresetKey = useAppSelector((state) => state.tradeSettings.selectedPreset[activeChain])
  const newConfigs = useAppSelector((state) => state.tradeSettings.settings)?.[activeChain]?.[selectedPresetKey - 1]
  const { handleSignMessage } = useSignWallet({
    isAutoConnect: false,
  })
  const {
    showToastProcessOrder,
    showToastSubmittedSuccessOrder,
    showErrorMessageSubmitOrder,
    newHiddenToastProcessOrder,
    isOrderProcessing,
  } = useShowToastOrder()
  const isOneClickTrading = ls.get('isOneClickTrading')
  const { maxcomputeUnit, priorityFeePrice } = useNetworkFeeSubcription()
  const setDefaultValue = () => {
    return {
      transactionType: TransactionType.Buy,
      type: OrderType.Market,
      baseAddress: '', //Token1
      quoteAddress: SOL_ADDRESS,
      userAddress: activeWallet?.walletId,
      quoteAmount: '',
      baseAmount: '',
      doublePrincipalAfterPurchase: false,
      chainId: ChainIds.Solana + '',
    }
  }

  const methods = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: setDefaultValue(),
  })
  const [totalToken, setTotalToken] = useState(0)
  const newHoldingToken = useWatchWalletTokenBalance({
    address: activeWallet?.walletId,
    token: tokenDetail?.address ?? '',
  })

  const { data: dataPortfolio } = useQuery(getPortfolio, {
    variables: {
      input: {
        userAddress: activeWallet?.walletId,
        token: tokenDetail?.address,
        hideSmallBalance: false,
        hideSmallLiquidity: false,
      },
    },
    skip: !activeWallet?.isConnected || !ServiceConfig.token || !tokenDetail?.address,
    client: gqlClient,
  })

  const dataPortfolioToken = dataPortfolio?.getPortfolio?.data?.[0] ?? {}

  useEffect(() => {
    if (!isEmpty(dataPortfolioToken)) {
      setTotalToken(dataPortfolioToken?.totalBaseAmount)
    }
  }, [dataPortfolioToken])

  useEffect(() => {
    if (newHoldingToken) {
      setTotalToken(+newHoldingToken?.balance)
    }
  }, [newHoldingToken])
  const { handleSubmit, setValue, watch } = methods
  const doublePrincipalAfterPurchase = watch('doublePrincipalAfterPurchase')
  const transactionType = watch('transactionType')
  const config = transactionType === TransactionType.Buy ? newConfigs?.buy : newConfigs?.sell

  const fee = useMemo(() => {
    if (config) {
      return config?.fee?.type === 'custom' ? config?.fee?.value : priorityFeePrice?.[config?.fee?.type]
    }
    return 0
  }, [config, priorityFeePrice, config?.fee?.value])

  useEffect(() => {
    if (orderType) {
      if (orderType === 'oneClick' || orderType === 'marketPrice') {
        setValue('type', OrderType.Market)
      }
      if (orderType === 'limitPrice') {
        setValue('type', OrderType.Limit)
      }
      if (orderType === 'trailingTpSl') {
        setValue('type', OrderType.TrailingTpsl)
      }
    }
  }, [orderType])

  const handleCheckErrorSubmitCreateOrder = (values: FormValues) => {
    if (values.transactionType === TransactionType.Buy) {
      if (activeWallet?.balance?.formatted < MIN_BALANCE_SOL) {
        toast.error(
          t('orderForm.errors.rechargeIfBalanceInsufficientMinimum', {
            chain: activeChain.toUpperCase(),
            balance: MIN_BALANCE_SOL,
          }),
        )
        return false
      }
      if (values?.quoteAmount > activeWallet?.balance?.formatted) {
        toast.error(t('orderForm.errors.rechargeIfBalanceInsufficient'))
        return false
      }
      const estimateQuoteAmount = +values?.quoteAmount * 1.01 + fee + BASIC_FEE
      if (estimateQuoteAmount > activeWallet?.balance?.formatted && values.transactionType === TransactionType.Buy) {
        toast.error(t('orderForm.errors.rechargeIfBalanceInsufficient'))
        return false
      }
    }

    if (+values?.quoteAmount < 0.000001) {
      toast.error(
        t('orderForm.errors.minimumOrderQuantity', {
          balance: 0.000001,
          chain: activeChain.toUpperCase(),
        }),
      )
      return false
    }

    if (!tokenDetail) {
      toast.error(t('orderForm.errors.selectTransactionQuantity'))
      return false
    }

    if (values.transactionType === TransactionType.Sell) {
      if (+values?.baseAmount === 0) {
        toast.error(t('orderForm.errors.noTokenAvailable'))
        return
      }
      if (+values?.baseAmount > totalToken) {
        toast.error(t('orderForm.errors.tokenInsufficient'))
        return
      }

      if (!values.callbackRate && values.type === OrderType.TrailingTpsl) {
        toast.error(t('orderForm.errors.selectTransactionQuantity'))
        return false
      }
    }
    return true
  }

  const createOrderByWallet = async (params: CreateOrderInput) => {
    const connection = solanaConnection
    if (params.transactionType === TransactionType.Buy) {
      const owner = new PublicKey(params?.userAddress)
      const tokenProgram = new PublicKey(TOKEN_PROGRAM_ID)
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(owner, {
        programId: tokenProgram,
      })
      let flag = 0
      for (let i = 0; i < tokenAccounts?.value.length; i++) {
        const data = tokenAccounts?.value?.[i]?.account?.data
        const tokenMint = data?.parsed?.info?.mint
        if (tokenMint === params.baseAddress) {
          flag = 1
          break
        }
      }

      if (flag === 0) {
        toast.info(
          t('orderForm.errors.firstTradeToken', {
            amount: FRIST_FEE_SOL,
            symbol: 'SOL',
          }),
        )

        const estimateQuoteAmount = +params?.quoteAmount * 1.01 + fee + FRIST_FEE_SOL + BASIC_FEE
        if (estimateQuoteAmount > activeWallet?.balance?.formatted) {
          toast.error(t('orderForm.errors.rechargeIfBalanceInsufficient'), {
            duration: 3500,
          })
          return
        }
      }
    }

    setIsLoading(true)
    const formatUnitBase = new BigNumber(10).pow(tokenDetail?.decimals ? +tokenDetail?.decimals : 6).toString() //tokenDetail?.decimals ? +tokenDetail?.decimals : 6
    const formatUnitQuote = new BigNumber(10).pow(9).toString()
    const isSell = params.transactionType === TransactionType.Sell
    const isBuy = params.transactionType === TransactionType.Buy
    const inputAmount = new BigNumber(
      params.transactionType === TransactionType.Buy ? params.quoteAmount : params.baseAmount,
    )
      .multipliedBy(isSell ? formatUnitBase : formatUnitQuote)
      .integerValue()
      .toString()
    const outputAmount = isBuy ? params.baseAmount : params.quoteAmount
    const inputAddress = isBuy ? params.quoteAddress : params.baseAddress
    const outputAddress = isBuy ? params.baseAddress : params.quoteAddress
    let amount = inputAmount
    let swapMode = 'ExactIn'
    if (inputAmount === 'NaN') {
      amount = outputAmount
      swapMode = 'ExactOut'
    }
    // if (swapMode === 'ExactIn') {
    //   amount = new BigNumber(inputAmount).dividedBy(1 - PLATFORM_FEE_SOL).toFixed(0)
    // }
    const slippage = new BigNumber((params.slippage ? params.slippage : 25) / 100).multipliedBy(10000).toString()
    const platformFee = new BigNumber(PLATFORM_FEE_SOL).multipliedBy(10000).toString()
    const quoteResponse = await (
      await fetch(
        `${Configs.urlApiJup}/swap/v1/quote?inputMint=${inputAddress}&outputMint=${outputAddress}&amount=${amount}&slippageBps=${slippage}&restrictIntermediateTokens=true&swapMode=${swapMode}&platformFeeBps=${platformFee}`,
      )
    ).json()

    if (quoteResponse?.error) {
      if (quoteResponse.errorCode === 'COULD_NOT_FIND_ANY_ROUTE') {
        showErrorMessageSubmitOrder(t('orderForm.status.ROUTE_NOT_FOUND'))
      } else {
        showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
      }
      setIsLoading(false)
      return
    }

    const publicKey = new PublicKey(wallet?.adapter?.publicKey!.toBytes() as PublicKeyInitData).toString()

    const feeAccount = await getAssociatedTokenAddress(
      new PublicKey(params?.quoteAddress),
      new PublicKey('CG8Pfaf6BR2xvBSXkmy8ecx7NMsWJWFKMwR8TcLwf9Az'),
    ).catch(() => {
      showErrorMessageSubmitOrder(t('orderForm.status.ROUTE_NOT_FOUND'))
      setIsLoading(false)
    })

    const swapResponse = await (
      await fetch(`${Configs.urlApiJup}/swap/v1/swap`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quoteResponse,
          userPublicKey: publicKey,
          dynamicComputeUnitLimit: false,
          dynamicSlippage: true,
          feeAccount: feeAccount,
        }),
      })
    ).json()

    if (swapResponse?.error) {
      showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
      setIsLoading(false)
      return
    }

    const swapTransactionBuf = Buffer.from(swapResponse.swapTransaction, 'base64')
    const txid = await sendTransaction(VersionedTransaction.deserialize(swapTransactionBuf), connection, {}).catch(
      () => {
        // console.log('[Error confirm txid]: ', res?.code)
        setIsLoading(false)
        showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
        return
      },
    )
    // console.log('[txid web3]: ', txid)
    const paramsWeb3 = {
      ...params,
      id: new Date().getDate() + '',
      baseSymbol: tokenDetail.symbol,
      marketCap: tokenDetail.marketCap,
      quoteSymbol: 'SOL',
    }

    if (txid) {
      showToastProcessOrder(paramsWeb3 as Order)
    }

    const latestBlockHash = await connection.getLatestBlockhash('processed').catch(() => {
      setIsLoading(false)
      showErrorMessageSubmitOrder(t('orderForm.status.RPC_CONNECTION'))
      return
    })

    const confirmation = await connection
      .confirmTransaction({
        blockhash: latestBlockHash.blockhash,
        lastValidBlockHeight: latestBlockHash.lastValidBlockHeight,
        signature: txid,
      })
      .catch(() => {
        showErrorMessageSubmitOrder(t('orderForm.status.TRANSACTION_BUILD_FAILED'))
        setIsLoading(false)
        return
      })

    // console.log('[Confirmation transaction]: ', confirmation)
    if (confirmation?.value?.err) {
      // console.error('Transaction failed:', confirmation?.value?.err)
      // showErrorMessageSubmitOrder(t('orderForm.status.RPC_CONNECTION'))
    } else {
      // console.log('Transaction confirmed!')
      if (txid) {
        dispatch(
          orderActions.createOrderByWeb3({
            input: {
              userAddress: paramsWeb3?.userAddress,
              txid: txid,
            },
          }),
        ).finally(() => {
          setIsLoading(false)
          newHiddenToastProcessOrder(paramsWeb3 as Order)
          showToastSubmittedSuccessOrder(paramsWeb3 as Order)
        })
      }
    }
  }

  const createOrderByTele = async (params: CreateOrderInput) => {
    setIsLoading(true)
    dispatch(orderActions.newCreateOrder({ input: params }))
      .then((res) => {
        if (res?.meta?.requestStatus === 'fulfilled') {
          const orderRes = res?.payload?.createOrder
          if (params?.type === OrderType.Market) {
            showToastProcessOrder(orderRes)
          } else {
            toast.success(t('orderForm.status.createOrderSuccess'))
          }
        } else {
          const errCode = res?.payload?.[0]?.code as string
          // console.log('errCode', errCode, res)
          showErrorMessageSubmitOrder(errCode ? t(`orderForm.status.${errCode}`) : t('orderForm.errors.orderFailed'))
        }
      })
      .finally(() => {
        setIsLoading(false)
      })
  }

  function onSubmit(values: FormValues) {
    if (activeAccount === TYPE_ACCOUNT.CHAIN && activeWallet.isConnected && !ServiceConfig.token) {
      handleSignMessage()
      toast.info(t('orderForm.errors.signMessageWalletInfo'))
      return
    }
    if (!handleCheckErrorSubmitCreateOrder(values)) return

    let params: CreateOrderInput = {} as CreateOrderInput
    params = {
      ...params,
      ...values,
      ...(!!config?.slippage && { slippage: ((config?.slippage !== 'auto' ? config?.slippage : 20) / 100).toString() }),
      quoteAmount: (+values.quoteAmount).toString(),
      baseAmount: (+values.baseAmount).toString(),
      mevProtect: config?.mevProtect,
      userAddress: activeWallet?.walletId,
      baseAddress: tokenDetail?.address ? tokenDetail?.address : '',
      priorityFeePrice: Math.floor((fee * Math.pow(10, 15)) / maxcomputeUnit) + '',
    } as CreateOrderInput

    if (orderType === 'trailingTpSl') {
      params.callbackRate = +values?.callbackRate / 100
    } else {
      delete params['callbackRate']
      delete params['triggerPrice']
      // delete params['limitPrice']
    }
    if (values?.limitPrice) {
      delete params['limitMarketCap']
    }
    if (values?.limitMarketCap) {
      delete params['limitPrice']
    }
    if (values.transactionType === TransactionType.Buy) {
      delete params['baseAmount']
    }
    if (values.transactionType === TransactionType.Sell) {
      delete params['quoteAmount']
      delete params['doublePrincipalAfterPurchase']
    }
    // console.log('[params create order]: ', params)
    if (activeAccount === TYPE_ACCOUNT.TELEGRAM) {
      createOrderByTele(params).catch(console.error)
    }
    if (activeAccount === TYPE_ACCOUNT.CHAIN) {
      if (activeChain === TYPE_CHAIN.ETH) {
        toast.info(t('orderForm.errors.changeTeleWalletToContinue'))
        return
      }
      createOrderByWallet(params).catch(console.error)
    }
  }

  const onConfirmSubmit = () => {
    const formValues = methods.getValues()
    onSubmit(formValues)
  }

  const isShowMessageTradeWeb3 =
    activeAccount === TYPE_ACCOUNT.CHAIN && (orderType === 'limitPrice' || orderType === 'trailingTpSl')
  return (
    <FormProvider {...methods}>
      <form
        className="w-[calc(61.12%-4px)] bg-[#1E1D1FCC] rounded-[6px] px-[6px] py-[8px] ml-auto"
        onSubmit={handleSubmit(onSubmit)}
      >
        {activeWallet.isConnected && (
          <FormSwitchWallet
            token={tokenDetail?.address}
            logoUrl={tokenDetail?.info?.logoUrl}
            symbol={tokenDetail?.symbol}
          />
        )}
        <div className="flex w-full mb-3 text-center bg-[#1c1b1d] relative">
          <div
            className={`w-[50%] rounded-l-full py-2 font-medium text-[14px] leading-none cursor-pointer ${transactionType === TransactionType.Buy ? 'bg-gradient-to-r from-[#01AC79] to-[#00e9a4] text-[#fff]' : 'bg-[#ECECED1F] text-[#d7d7d7]'}`}
            onClick={() => {
              setValue('transactionType', TransactionType.Buy)
              if (orderType === 'trailingTpSl') {
                setOrderType('oneClick')
              }
            }}
            style={{ clipPath: 'polygon(0px 0px, 100% 0px, 97% 100%, 0px 100%)' }}
          >
            {t('orderForm.tabs.buy')}
          </div>
          <div
            className={`flex-1 rounded-r-full py-2 font-medium text-[14px] leading-none cursor-pointer ${transactionType === TransactionType.Sell ? 'bg-gradient-to-r from-[#9035FF] to-[#EE69FF] text-[#fff]' : 'bg-[#ECECED1F] text-[#d7d7d7]'}`}
            onClick={() => {
              setValue('transactionType', TransactionType.Sell)
            }}
            style={{ clipPath: 'polygon(3% 0px, 100% 0px, 100% 100%, 0% 100%)' }}
          >
            {t('orderForm.tabs.sell')}
          </div>
        </div>

        <FilterSelect
          options={transactionType === TransactionType.Buy ? orderTypesOptions : orderSellOptions}
          value={orderType}
          onValueChange={(value) => {
            setOrderType(value as DetailOrderType)
          }}
          selectTriggerProps={{
            className: cn(
              'w-full max-w-auto justify-center bg-[#ECECED14] rounded-[4px] border-none relative p-[7px] app-font-medium leading-[1rem])] text-white mb-[12px]',
              lang === 'en' ? '[&>span>div]:leading-[16px] !py-1.5' : '',
            ),
          }}
          triggerIconClassname="w-[8.42px] h-[5.14px] absolute top-[50%] translate-y-[-50%] right-[7.69px]"
          triggerIcon="/images/orderForm/icon-dropdown.svg"
          childrenTrigger={
            <>
              {orderType === 'limitPrice' && (
                <TooltipProvider delayDuration={200}>
                  <Tooltip>
                    <TooltipTrigger className="absolute top-1/2 left-1 translate-y-[-50%]">
                      <img src="/images/orderSetting/icon-info.svg" className="w-[16px] min-w-[16px]" alt="" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-[360px] bg-[#191919]">
                      <p className="text-[11px] tracking-wide">{t('orderForm.form.limitCommissionTelegram')}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              {orderType === 'trailingTpSl' && (
                <TooltipProvider delayDuration={200}>
                  <Tooltip>
                    <TooltipTrigger className="absolute top-1/2 left-1 translate-y-[-50%]">
                      <img src="/images/orderSetting/icon-info.svg" className="w-[16px] min-w-[16px]" alt="" />
                    </TooltipTrigger>
                    <TooltipContent className="max-w-[360px] bg-[#191919]">
                      <p className="text-[11px] tracking-wide">
                        {t('orderForm.form.supportHoldText')}
                        <br />
                        {t('orderForm.form.supportTextListItem1')}
                        <br />
                        {t('orderForm.form.supportTextListItem2')}
                        <br />
                        {t('orderForm.form.supportTextListItem3')}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </>
          }
        />
        {orderType === 'oneClick' && <FormOneClick tokenDetail={tokenDetail} totalToken={totalToken} />}
        {orderType === 'marketPrice' && <FormMarketPrice tokenDetail={tokenDetail} totalToken={totalToken} />}
        {orderType === 'limitPrice' && <FormLimitPrice tokenDetail={tokenDetail} totalToken={totalToken} />}
        {orderType === 'trailingTpSl' && <FormTrailing tokenDetail={tokenDetail} totalToken={totalToken} />}

        <div className="flex items-center gap-[4px] mt-[34px] mb-[8px]">
          {activeAccount === TYPE_ACCOUNT.TELEGRAM && transactionType === TransactionType.Buy && (
            <>
              <CheckboxWithLabel
                defaultChecked={doublePrincipalAfterPurchase}
                label={t('orderForm.form.doublePrincipalAfterPurchase')}
                onChange={(value) => {
                  setValue('doublePrincipalAfterPurchase', !!value)
                }}
              />
              <TooltipProvider delayDuration={200}>
                <Tooltip>
                  <TooltipTrigger>
                    <img src="/images/orderSetting/icon-info.svg" className="w-[12px] min-w-[12px]" alt="" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[360px]">
                    <p className="text-xs leading-[16px]">{t('walletDetail.tooltip.doublePrincipal')}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </>
          )}
        </div>
        <TradeSettingsBottomSheet open={openTradeSettings} setOpen={setOpenTradeSettings} type={2} />
        {isOneClickTrading ? (
          <>
            {isShowMessageTradeWeb3 ? (
              <p className="text-[#FFFFFF99] text-[calc(1rem*14/16)] pt-[5px] pb-[2px]">
                {t('orderForm.form.errExternalWallet')}
              </p>
            ) : (
              <Button
                disabled={isLoading || !activeWallet?.isConnected || isShowMessageTradeWeb3 || isOrderProcessing}
                type="submit"
                className={cn(
                  "rounded-full w-full mt-[10px] hover-scale text-[#fff]",
                  transactionType === TransactionType.Sell
                    ? 'bg-gradient-to-r from-[#9035FF] to-[#EE69FF]'
                    : 'bg-gradient-to-r from-[#01AC79] to-[#00e9a4]'
                )}
                onClick={onConfirmSubmit}
              >
                {transactionType === TransactionType.Sell ? t('orderForm.form.confirmSell') : t('orderForm.form.confirmBuy')}
              </Button>
            )}
          </>
        ) : (
          <PopupConfirmSubmitOrder
            transactionType={transactionType}
            onConfirmSubmit={() => onConfirmSubmit()}
            disabled={isLoading || !activeWallet?.isConnected || isShowMessageTradeWeb3 || isOrderProcessing}
            isShowMessageTradeWeb3={isShowMessageTradeWeb3}
          />
        )}
      </form>
    </FormProvider>
  )
}

export default OrderForm

const PopupConfirmSubmitOrder = ({
  transactionType,
  onConfirmSubmit,
  disabled,
  isShowMessageTradeWeb3,
}: {
  transactionType: string
  onConfirmSubmit: () => void
  disabled: boolean
  isShowMessageTradeWeb3: boolean
}) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const [checked, setChecked] = useState(ls.get('isOneClickTrading') ?? false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild className={isShowMessageTradeWeb3 ? 'pointer-events-none' : ''}>
        {isShowMessageTradeWeb3 ? (
          <p className="text-[#FFFFFF99] text-[calc(1rem*14/16)] pt-[5px] pb-[2px]">
            {t('orderForm.form.errExternalWallet')}
          </p>
        ) : (
          <Button
            disabled={disabled}
            className={cn(
              "rounded-full w-full mt-[10px] hover-scale text-[#fff]",
              transactionType === TransactionType.Sell
                ? '!bg-gradient-to-r !from-[#9035FF] !to-[#EE69FF]'
                : '!bg-gradient-to-r !from-[#01AC79] !to-[#00e9a4]'
            )}
          >
            {transactionType === TransactionType.Sell ? t('orderForm.form.confirmSell') : t('orderForm.form.confirmBuy')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="w-[335px] bg-[#232329] rounded-2xl p-5">
        <DialogTitle></DialogTitle>
        <DialogHeader>
          <p className="text-lg leading-none font-medium pt-3">{t('orderForm.form.confirmBuying')}?</p>
          <div className="flex items-center gap-1.5 mt-2">
            <CheckboxWithLabel
              label={t('orderForm.form.noMorePrompt')}
              defaultChecked={checked}
              onChange={(value) => setChecked(value)}
              containerClassName="mt-0.5"
              labelWrapperClassName="text-[15px] text-[#ffffffb3]"
            />
          </div>
          <div className="flex justify-center items-center flex-row gap-2.5 mt-4">
            <Button variant="close" className="flex-1" onClick={() => setOpen(false)}>
              {t('orderForm.form.cancel')}
            </Button>
            <Button
              variant="gradient"
              className="text-[#261236] flex-1 rounded-[50px]"
              onClick={() => {
                ls.set('isOneClickTrading', checked)
                onConfirmSubmit()
                setOpen(false)
              }}
            >
              {t('orderForm.form.sure')}
            </Button>
          </div>
        </DialogHeader>
        <DialogDescription />
      </DialogContent>
    </Dialog>
  )
}
