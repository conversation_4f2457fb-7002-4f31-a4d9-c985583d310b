
import { <PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Drawer<PERSON>rigger } from '@/components/ui/drawer'
import { useState, useEffect, useMemo, ReactNode, useRef } from 'react'
import ListAppShare from '@components/common/share/listAppShare'
import html2canvas from 'html2canvas'
import ShareFooter from '@/components/common/share/shareFooter'

interface sharePrpos {
  info: any,
  childrenTrigger: string | ReactNode
}

interface StatCardData {
  label: string
  value: string | ReactNode
}

interface StatCardProps {
  data: StatCardData
}



const share = ({ info, childrenTrigger }: sharePrpos) => {
  // console.log(info, 'ffff')
  const [open, setOpen] = useState(false)
  const posterRef = useRef<HTMLDivElement>(null)

  // 渲染统计卡片的方法
  const renderStatCard = (data: StatCardData) => {
    return (
      <div style={{
        textAlign: "center",
        margin: "0",
        backgroundColor: "rgba(26, 26, 26, 0.024)", // #1A1A1A06
        border: "1px solid rgba(236, 236, 237, 0.078)", // #ECECED14
        paddingTop: "10px",
        paddingBottom: "10px",
        borderRadius: "4px"
      }}>
        <p style={{
          color: "rgba(255, 255, 255, 0.44)", // #FFFFFF70
          fontSize: "11px",
          margin: "0 0 4px 0",
          lineHeight: "1.2"
        }}>{data.label}</p>
        <div style={{
          color: "#FFFFFF",
          fontSize: "13px",
          margin: "0",
          lineHeight: "1.2",
          fontWeight: "500"
        }}>{data.value}</div>
      </div>
    )
  }

  // 统计数据配置
  const statData: StatCardData[] = [
    {
      label: '未实现利润',
      value: '79,178.37'
    },
    {
      label: '总手续费',
      value: '189,778.34'
    },
    {
      label: '30D交易量',
      value: (
        <div style={{
          fontSize: "14px",
          lineHeight: "1.2",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          gap: "2px"
        }}>
          <span style={{ color: "#00FFB4" }}>2323</span>
          <span style={{
            color: "rgba(255, 255, 255, 0.44)",
            fontSize: "12px",
            lineHeight: "1"
          }}>/</span>
          <span style={{ color: "#AB57FF" }}>3434</span>
        </div>
      )
    }
  ]

  const onSave = async () => {
    if (!posterRef.current) return

    // 等待所有图片加载完成
    const images = posterRef.current.querySelectorAll('img')
    await Promise.all(Array.from(images).map(img => {
      if (img.complete) return Promise.resolve()
      return new Promise(resolve => {
        img.onload = resolve
        img.onerror = resolve
      })
    }))

    const canvas = await html2canvas(posterRef.current, {
      useCORS: true,
      allowTaint: true,
      scale: 2, // 提高分辨率
      backgroundColor: '#01020B', // 设置背景色而不是透明
      logging: false,
      width: posterRef.current.offsetWidth,
      height: posterRef.current.offsetHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: posterRef.current.offsetWidth,
      windowHeight: posterRef.current.offsetHeight,
      foreignObjectRendering: false, // 关闭以避免渲染问题
      removeContainer: false, // 保持容器
      imageTimeout: 15000, // 图片加载超时
      onclone: (clonedDoc) => {
        // 在克隆的文档中强制应用样式
        const clonedElement = clonedDoc.querySelector('[data-html2canvas-ignore]')
        if (clonedElement) {
          clonedElement.remove()
        }
      }
    })

    const link = document.createElement('a')
    link.download = 'share.png'
    link.href = canvas.toDataURL('image/png', 1.0)
    link.click()
  }



  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{childrenTrigger}</DrawerTrigger>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto">
        <DrawerHeader className="py-5 px-3.5 flex w-full items-center justify-between">
          <DrawerTitle>分享</DrawerTitle>
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt="close"
          />
        </DrawerHeader>
        <div className=" px-6 space-y-6 ">
          <div className=" bg-[#01020B]"
            ref={posterRef}>

            {/* 渐变卡片 */}
            <div style={{
              position: "relative",
              borderBottomLeftRadius: "8px",
              borderBottomRightRadius: "8px",
              textAlign: "center",
              color: "#FFFFFF",
              backgroundImage: "url('/images/share/title-bg.png')",
              backgroundSize: "cover",
              backgroundRepeat: "no-repeat",
              width: "100%",
              paddingTop: "25px",
              fontWeight: "bold",
              height: "120px"
            }}>
              <img src="/images/share/gold-one.png" alt="" style={{
                position: "absolute",
                top: "0",
                left: "0"
              }} />
              <p style={{
                fontSize: "22px", // calc(22rem/16) = 22px
                marginBottom: "5px",
                margin: "0 0 5px 0",
                lineHeight: "1.2"
              }}>去中心化交易所 Mobile No.1</p>
              <p style={{
                fontSize: "22px", // calc(22rem/16) = 22px
                margin: "0",
                lineHeight: "1.2"
              }}>毫秒级行情 极速交易</p>
            </div>

            {/* 数据展示卡片 */}
            <div className="relative overflow-hidden bg-[url('/images/share/container-bg.png') ] bg-cover bg-no-repeat" style={{ padding: "20px" }}>
              <img src="/images/share/ribbons.png" alt="" className='absolute top-[-70px] right-0 w-full' />
              <img src="/images/share/container-bg.png" alt="" className='absolute top-0 left-0 w-full' />
              <img src="/images/share/gold-two.png" alt="" className='absolute top-[180px] right-0' />
              {/* 主要数据 */}
              <div className="relative z-10">
                <h3 style={{ marginBottom: "16px", color: "#FFFFFF", fontSize: "30px", fontWeight: "bold" }}>30D已实现利润</h3>
                <div style={{ marginBottom: "16px" }}>
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "10px", // 替换 marginLeft
                  }}>
                    <span style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#00FFB4",
                      lineHeight: "1",
                    }}>
                      $482,12.12
                    </span>
                    <span style={{
                      padding: "2px 4px",
                      display: "inline-flex", // 使用 inline-flex
                      alignItems: "center", // 内部也居中
                      fontSize: "14px",
                      borderRadius: "4px",
                      background: "#00FFB4",
                      color: "#1A1A1A",
                      lineHeight: "1",
                    }}>
                      12.34%
                    </span>
                  </div>
                  <div style={{
                    color: "rgba(255, 255, 255, 0.5)", // #FFFFFF80
                    fontSize: "14px", // text-sm
                    marginTop: "16px",
                    lineHeight: "1.2"
                  }}>
                    连线包地址
                    <span style={{
                      color: "#FFFFFF",
                      marginLeft: "8px" // ml-2
                    }}>45hdd...6yt</span>
                  </div>
                </div>
                {/* 统计数据 */}
                <div style={{
                  display: "flex",
                  justifyContent: "space-between",
                  gap: "10px" // gap-2.5 = 10px
                }}>
                  {statData.map((item, index) => (
                    <div key={index} style={{ flex: "1" }}>
                      {renderStatCard(item)}
                    </div>
                  ))}
                </div>
                <div className="relative h-[175px] z-10">
                  <img src="/images/share/modify_img.png" alt="" className='absolute top-[-25px] right-[-30px] w-[203px] h-[200px]' />
                </div>
              </div>
            </div>
            <ShareFooter />
          </div>
          <ListAppShare onSave={() => onSave()} />
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default share
