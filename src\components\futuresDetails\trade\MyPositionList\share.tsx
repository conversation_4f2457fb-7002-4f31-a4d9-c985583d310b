
import { Drawer, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from '@/components/ui/drawer'
import { useState, useEffect, useMemo, ReactNode, useRef } from 'react'
import ListAppShare from '@components/common/share/listAppShare'
import html2canvas from 'html2canvas'
import ShareFooter from '@/components/common/share/shareFooter'

interface sharePrpos {
  info: any,
  childrenTrigger: string | ReactNode
}

interface StatCardData {
  label: string
  value: string | ReactNode
}

interface StatCardProps {
  data: StatCardData
}



const share = ({ info, childrenTrigger }: sharePrpos) => {
  // console.log(info, 'ffff')
  const [open, setOpen] = useState(false)
  const posterRef = useRef<HTMLDivElement>(null)

  // 渲染统计卡片的方法
  const renderStatCard = (data: StatCardData) => {
    return (
      <div className="text-center m-0 bg-[#1A1A1A06] border-[#ECECED14] border-solid border" style={{ paddingTop: "10px", paddingBottom: "10px" }}>
        <p className="text-[#FFFFFF70]" style={{ fontSize: '11px' }}>{data.label}</p>
        <p className="text-white" style={{ fontSize: '13px' }}>{data.value}</p>
      </div>
    )
  }

  // 统计数据配置
  const statData: StatCardData[] = [
    {
      label: '未实现利润',
      value: '79,178.37'
    },
    {
      label: '总手续费',
      value: '189,778.34'
    },
    {
      label: '30D交易量',
      value: (
        <div className="text-[calc(14rem/16)]">
          <span className='text-rise'>2323</span><span className='text-[#FFFFFF70] size-[0.75em]'>/</span><span className='text-fall'>3434</span>
        </div>
      )
    }
  ]

  const onSave = async () => {
    if (!posterRef.current) return
    const canvas = await html2canvas(posterRef.current, {
      useCORS: true,
      allowTaint: true,
      scale: 2, // 提高分辨率
      backgroundColor: null, // 保持透明背景
      logging: false,
      width: posterRef.current.offsetWidth,
      height: posterRef.current.offsetHeight,
      scrollX: 0,
      scrollY: 0,
      windowWidth: posterRef.current.offsetWidth,
      windowHeight: posterRef.current.offsetHeight,
      // foreignObjectRendering: true, // 重要：更好的文本渲染
      removeContainer: true, // 移除容器影响
    })

    const link = document.createElement('a')
    link.download = 'share.png'
    link.href = canvas.toDataURL('image/png', 1.0)
    link.click()
  }



  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{childrenTrigger}</DrawerTrigger>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto">
        <DrawerHeader className="py-5 px-3.5 flex w-full items-center justify-between">
          <DrawerTitle>分享</DrawerTitle>
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt="close"
          />
        </DrawerHeader>
        <div className=" px-6 space-y-6 ">
          <div className=" bg-[#01020B]"
            ref={posterRef}>

            {/* 渐变卡片 */}
            <div className="relative rounded-b-lg text-center  text-white bg-[url('/images/share/title-bg.png')] 
              bg-cover bg-no-repeat  w-full" style={{ paddingTop: "25px", fontWeight: "bold", height: '120px' }}>
              <img src="/images/share/gold-one.png" alt="" className='absolute top-0 left-0' />
              <p className="text-[calc(22rem/16)]" style={{ marginBottom: "5px" }}>去中心化交易所 Mobile No.1</p>
              <p className="text-[calc(22rem/16)] ">毫秒级行情 极速交易</p>
            </div>

            {/* 数据展示卡片 */}
            <div className="relative overflow-hidden bg-[url('/images/share/container-bg.png') ] bg-cover bg-no-repeat" style={{ padding: "20px" }}>
              <img src="/images/share/ribbons.png" alt="" className='absolute top-[-70px] right-0 w-full' />
              <img src="/images/share/container-bg.png" alt="" className='absolute top-0 left-0 w-full' />
              <img src="/images/share/gold-two.png" alt="" className='absolute top-[180px] right-0' />
              {/* 主要数据 */}
              <div className="relative z-10">
                <h3 style={{ marginBottom: "16px", color: "#FFFFFF", fontSize: "30px", fontWeight: "bold" }}>30D已实现利润</h3>
                <div style={{ marginBottom: "16px" }}>
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "10px", // 替换 marginLeft
                  }}>
                    <span style={{
                      fontSize: "24px",
                      fontWeight: "bold",
                      color: "#00FFB4",
                      lineHeight: "1",
                    }}>
                      $482,12.12
                    </span>
                    <span style={{
                      padding: "2px 4px",
                      display: "inline-flex", // 使用 inline-flex
                      alignItems: "center", // 内部也居中
                      fontSize: "14px",
                      borderRadius: "4px",
                      background: "#00FFB4",
                      color: "#1A1A1A",
                      lineHeight: "1",
                    }}>
                      12.34%
                    </span>
                  </div>
                  <div className="text-[#FFFFFF80] text-sm" style={{ marginTop: "16px" }}>
                    连线包地址
                    <span className='text-white ml-2'>45hdd...6yt</span>
                  </div>
                </div>
                {/* 统计数据 */}
                <div className="flex justify-between gap-2.5" >
                  {statData.map((item, index) => (
                    <div key={index} className="flex-1">
                      {renderStatCard(item)}
                    </div>
                  ))}
                </div>
                <div className="relative h-[175px] z-10">
                  <img src="/images/share/modify_img.png" alt="" className='absolute top-[-25px] right-[-30px] w-[203px] h-[200px]' />
                </div>
              </div>
            </div>
            <ShareFooter />
          </div>
          <ListAppShare onSave={() => onSave()} />
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default share
