
import { Drawer, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>er<PERSON><PERSON><PERSON>, DrawerTrigger } from '@/components/ui/drawer'
import { useState, useEffect, useMemo, ReactNode, useRef } from 'react'
import ListAppShare from '@components/common/share/listAppShare'
import html2canvas from 'html2canvas'
import ShareFooter from '@/components/common/share/shareFooter'

interface sharePrpos {
  info: any,
  childrenTrigger: string | ReactNode
}

interface StatCardData {
  label: string
  value: string | ReactNode
}

interface StatCardProps {
  data: StatCardData
}



const share = ({ info, childrenTrigger }: sharePrpos) => {
  // console.log(info, 'ffff')
  const [open, setOpen] = useState(true)
  const posterRef = useRef<HTMLDivElement>(null)

  // 渲染统计卡片的方法
  const renderStatCard = (data: StatCardData) => {
    return (
      <div className="text-center bg-[#1A1A1A06] border-[#ECECED14] border-solid border p-2">
        <div className="text-[#FFFFFF70] text-[calc(11rem/16)]">{data.label}</div>
        <div className="text-white text-[calc(14rem/16)]">{data.value}</div>
      </div>
    )
  }

  // 统计数据配置
  const statData: StatCardData[] = [
    {
      label: '未实现利润',
      value: '79,178.37'
    },
    {
      label: '总手续费',
      value: '189,778.34'
    },
    {
      label: '30D交易量',
      value: (
        <div className="text-[calc(14rem/16)]">
          <span className='text-rise'>2323</span><span className='text-[#FFFFFF70] size-[0.75em]'>/</span><span className='text-fall'>3434</span>
        </div>
      )
    }
  ]


  const onSave = async () => {
    if (!posterRef.current) return
    const canvas = await html2canvas(posterRef.current, {
      useCORS: true,
      scale: 2,
    })
    const link = document.createElement('a')
    link.download = 'share.png'
    link.href = canvas.toDataURL()
    link.click()
  }



  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <DrawerTrigger asChild>{childrenTrigger}</DrawerTrigger>
      <DrawerContent className="w-full bg-[#232329] max-w-[768px] mx-auto">
        <DrawerHeader className="py-5 px-3.5 flex w-full items-center justify-between">
          <DrawerTitle>分享</DrawerTitle>
          <img
            src="/images/icons/icon-x.svg"
            className="w-6 h-6 cursor-pointer"
            onClick={() => setOpen(false)}
            alt="close"
          />
        </DrawerHeader>
        <div className=" px-6 space-y-6 ">
          <div className="
              bg-[url('/images/share/share-bg.png')] 
              bg-cover 
              bg-center 
              bg-no-repeat 
              relative 
              before:absolute 
              before:inset-0 
              before:bg-black/50 
              before:z-[-1]
              // w-[300px]
          " 
          ref={posterRef}>
            
            {/* 渐变卡片 */}
            <div className="relative rounded-b-lg">
               <div className="absolute inset-0">
                {/* <div className="w-full h-full" style={{
                  backgroundImage: `
                    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 2px),
                    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 2px)
                  `,
                  backgroundSize: '30px 30px'
                }}></div> */}
              </div>
              <p className="text-[calc(22rem/16)] font-bold mb-1">去中心化交易所 Mobile No.1</p>
              <p className="text-[calc(22rem/16)] font-bold ">毫秒级行情 极速交易</p>
            </div>

            {/* 数据展示卡片 */}
            <div className="rounded-2xl p-6 relative overflow-hidden">
              {/* 背景网格 */}
              {/* <div className="absolute inset-0 top-[-30%] left-[-44%] rotate-10 opacity-20">
                <div className="w-full h-full" style={{
                  backgroundImage:'url(/images/share/bg.png)',
                  backgroundSize:'100%',
                  backgroundRepeat:'no-repeat'
                }}></div> */}
              </div>

              {/* 主要数据 */}
              <div className="relative z-10 ">
                <h3 className="text-[calc(32rem/16)] font-bold mb-2">30D已实现利润</h3>
                <div className="mb-6">
                  <div className='flex items-center  mb-2'>
                  <div className="text-[calc(24rem/16)] font-bold text-[#00FFB4]">
                    $482,12.12
                  </div>
                   <div className="text-[calc(14rem/16)] ml-2 bg-[#00FFB4] text-[#1A1A1A] px-2 rounded">
                      12.34%
                    </div>
                  </div>
                  <div className="text-[#FFFFFF80] text-sm">
                    连线包地址
                    <span className='text-white ml-2'>45hdd...6yt</span>
                  </div>
                </div>

                {/* 统计数据 */}
                <div className="grid grid-cols-3 gap-2 mb-6">
                  {statData.map((item, index) => (
                    <div key={index}>
                      {renderStatCard(item)}
                    </div>
                  ))}
                </div>

                {/* 3D立方体图标 */}
                 <div className="flex justify-center mb-6">
                  <div className="relative w-24 h-24">
                    {/* 这里可以放置3D立方体SVG或图片 */}
                    <div className="w-full h-full bg-gradient-to-br from-purple-500 to-cyan-400 rounded-lg transform rotate-12 shadow-lg flex items-center justify-center">
                      <div className="w-8 h-8 bg-white rounded opacity-80"></div>
                    </div>                 
                </div>
                </div>
               
               
              </div>
            </div>
            <ShareFooter/>
          </div>
          <ListAppShare onSave={() => onSave()} />
          {/* 底部分享按钮 */}
          {/* <div className="fixed bottom-0 left-0 right-0 bg-gray-900 p-6">
            <div className="flex justify-center space-x-8">
              <button className="flex flex-col items-center space-y-2">
                <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                  </svg>
                </div>
                <span className="text-xs text-gray-400">保存相册</span>
              </button>
              <button className="flex flex-col items-center space-y-2">
                <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                  </svg>
                </div>
                <span className="text-xs text-gray-400">Twitter(X)</span>
              </button>
              <button className="flex flex-col items-center space-y-2">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
                  </svg>
                </div>
                <span className="text-xs text-gray-400">Telegram</span>
              </button>
              <button className="flex flex-col items-center space-y-2">
                <div className="w-12 h-12 bg-gray-700 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                  </svg>
                </div>
                <span className="text-xs text-gray-400">更多</span>
              </button>
            </div>
          </div> */}
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export default share
