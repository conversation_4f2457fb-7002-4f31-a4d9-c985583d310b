import { Position, UserPositionResponse } from '@/@generated/gql/graphql-symbolDex'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { useQuery } from '@apollo/client'
import { getUserPosition } from '@services/assets.service.ts'
import { useMemo } from 'react'
import ItemPosition, { ItemPositionSkeleton } from './ItemPosition'
import PositionsOverview from './PositionsOverview'

const TabPositions = () => {
  const { data, loading } = useQuery<UserPositionResponse>(getUserPosition, {
    client: symbolDexClient,
  })

  const userPosition = useMemo(() => {
    if (!data) return null
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    //@ts-ignore
    return data?.getUserPosition
  }, [data])

  return (
    <>
      <div className="p-[10px] rounded-t-[10px] bg-[#14141480] relative bg-[url('/images/assets/futures/bg.svg')] bg-cover bg-center bg-no-repeat mb-80 ">
        <PositionsOverview data={userPosition} isLoading={loading} />
        <div className="mt-4 border-t border-[#ECECED14] pt-4 flex flex-col gap-2">
          {loading ? (
            <>
              <ItemPositionSkeleton />
              <ItemPositionSkeleton />
              <ItemPositionSkeleton />
            </>
          ) : (
            userPosition?.positions.map((item: Position, index: number) => <ItemPosition key={index} item={item} />)
          )}
        </div>
      </div>
    </>
  )
}

export default TabPositions
