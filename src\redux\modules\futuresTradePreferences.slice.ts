import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit'
import { REHYDRATE, RehydrateAction } from 'redux-persist'

export type DepthLayout = 'showAll' | 'showAsks' | 'showBids'

export interface FuturesTradePreferencesState {
  preferences: {
    depthUnit: 'base' | 'quote'
    depthLayout: DepthLayout
    depthPrecision: number
    isExpandKline: boolean

    allSymbols: string[]
  }
}

export const initialStateTradeConfig: FuturesTradePreferencesState = {
  preferences: {
    depthUnit: 'base',
    depthLayout: 'showAll',
    depthPrecision: 0.1,
    isExpandKline: false,
    allSymbols: [],
  },
}

export const futuresTradePreferencesSlice = createSlice({
  name: 'futuresTradePreferencesSetting',
  initialState: initialStateTradeConfig,
  reducers: {
    updateTradePreferences: (
      state,
      action: PayloadAction<Partial<FuturesTradePreferencesState['preferences']>>
    ) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload,
      }
    },
    updateAllSymbols: (state, action: PayloadAction<string[]>) => {
      state.preferences.allSymbols = action.payload
    }
  },
  extraReducers: (builder) => {
    builder.addCase(REHYDRATE, (state, action: RehydrateAction) => {
      localStorage.removeItem('persist:futuresTradePreferences')
    })
  },
})

export const { updateTradePreferences, updateAllSymbols } = futuresTradePreferencesSlice.actions
export const futuresTradePreferencesActions = { ...futuresTradePreferencesSlice.actions }

export const selectFuturesTradePreferences = (state: { futuresTradePreferences: FuturesTradePreferencesState }) =>
  state.futuresTradePreferences.preferences

export default futuresTradePreferencesSlice
