import { useSubscription } from '@/lib/mqtt'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { TokenPortrait } from '@/@generated/gql/graphql-core.ts'

export interface TokenAlertProps {
  address?: string
  tokenPortrait?: TokenPortrait
}

export const TokenAlert = (props: TokenAlertProps) => {
  const { address, tokenPortrait } = props
  const [isLowLiquidity, setIsLowLiquidity] = useState(false)
  const { t } = useTranslation()
  const { message: lowLPMessage } = useSubscription(`public/alerts/low_lp/${address}`, {
    shouldSkip: !address,
  })

  useEffect(() => {
    const msgString = lowLPMessage?.message?.toString()
    if (!msgString) return
    const payload = JSON.parse(msgString)
    const lowLP = payload.low_lp as boolean
    setIsLowLiquidity(lowLP ?? false)
  }, [lowLPMessage])

  useEffect(() => {
    setIsLowLiquidity(tokenPortrait?.lowLiquidity ?? false)
  }, [tokenPortrait])

  if (!isLowLiquidity) {
    return null
  }

  return (
    <div className="bg-[linear-gradient(90deg,#A53EFF33_0%,#A53EFF14_100%)] flex items-center px-2.5 py-2">
      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M7 8.60417C6.76083 8.60417 6.5625 8.40583 6.5625 8.16667V5.25C6.5625 5.01083 6.76083 4.8125 7 4.8125C7.23917 4.8125 7.4375 5.01083 7.4375 5.25V8.16667C7.4375 8.40583 7.23917 8.60417 7 8.60417Z"
          fill="#FF353C"
        />
        <path
          d="M6.99935 10.5002C6.96435 10.5002 6.92352 10.4943 6.88268 10.4885C6.84768 10.4827 6.81268 10.471 6.77768 10.4535C6.74268 10.4418 6.70768 10.4243 6.67268 10.401C6.64352 10.3777 6.61435 10.3543 6.58518 10.331C6.48018 10.2202 6.41602 10.0685 6.41602 9.91682C6.41602 9.76516 6.48018 9.61349 6.58518 9.50266C6.61435 9.47932 6.64352 9.45599 6.67268 9.43266C6.70768 9.40932 6.74268 9.39182 6.77768 9.38016C6.81268 9.36266 6.84768 9.35099 6.88268 9.34516C6.95852 9.32766 7.04018 9.32766 7.11018 9.34516C7.15102 9.35099 7.18602 9.36266 7.22102 9.38016C7.25602 9.39182 7.29102 9.40932 7.32602 9.43266C7.35518 9.45599 7.38435 9.47932 7.41352 9.50266C7.51852 9.61349 7.58268 9.76516 7.58268 9.91682C7.58268 10.0685 7.51852 10.2202 7.41352 10.331C7.38435 10.3543 7.35518 10.3777 7.32602 10.401C7.29102 10.4243 7.25602 10.4418 7.22102 10.4535C7.18602 10.471 7.15102 10.4827 7.11018 10.4885C7.07518 10.4943 7.03435 10.5002 6.99935 10.5002Z"
          fill="#FF353C"
        />
        <path
          d="M10.5341 12.9266H3.46413C2.32663 12.9266 1.45746 12.5124 1.01413 11.7657C0.576631 11.0191 0.634965 10.0566 1.18913 9.05908L4.72413 2.70074C5.30746 1.65074 6.11246 1.07324 6.99913 1.07324C7.8858 1.07324 8.6908 1.65074 9.27413 2.70074L12.8091 9.06491C13.3633 10.0624 13.4275 11.0191 12.9841 11.7716C12.5408 12.5124 11.6716 12.9266 10.5341 12.9266ZM6.99913 1.94824C6.4508 1.94824 5.91413 2.36824 5.4883 3.12658L1.95913 9.49074C1.56246 10.2024 1.4983 10.8557 1.77246 11.3282C2.04663 11.8007 2.6533 12.0574 3.46996 12.0574H10.54C11.3566 12.0574 11.9575 11.8007 12.2375 11.3282C12.5175 10.8557 12.4475 10.2082 12.0508 9.49074L8.50996 3.12658C8.08413 2.36824 7.54746 1.94824 6.99913 1.94824Z"
          fill="#FF353C"
        />
      </svg>
      <div className="text-white text-[calc(12rem/16)] ml-2 leading-4">{t('detail.alert.lowLiquidity')}</div>
    </div>
  )
}
