import { useCallback, useEffect, useMemo, useState, useRef } from 'react'
import HeaderSetting from './HeaderSetting'
import Chart from '../chart/index'
import Container from '@components/common/Container.tsx'
import OrderBook from './OrderBook'
import LastestTarde from './LastestTarde'
import OrderForm from './OrderForm'
import LeftSetting from './LeftSetting'
import { UITab } from '@/types/uiTabs.ts'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import CurrentOrderList from './CurrentOrderList'
import MyPositionList from './MyPositionList'
import OrderHistoryList from './OrderHistoryList'
import { Button } from '@/components/ui/button'
import { getPredictedFundings, getPerpMetadata } from '@/api/hyperliquid'
import { updateUserFunding } from '@/redux/modules/futuresUserInfo.slice'
import { PredictedFunding } from '@/types/hyperliquid.ts'
import { useMultiChainWallet } from '@hooks/useMultiChainWallet.ts'
import FeeCountdown from './FeeCountdown'
import { RootState, useAppSelector, useAppDispatch } from '@/redux/store'
import { useWebData2 } from '@/hooks/hyperliquid/useWebData2'
import { useUserFillsData } from '@/hooks/hyperliquid/useUserFillsData'
import DetailSymbol from '../DetailSymbol'
import { selectAllPerpMeta, setSymbolListCtxs  } from '@/redux/modules/futuresMeta.slice'
import { OpenOrdersContext } from './OpenOrdersContext'
import { xOpenOrders, xPositions } from './types'
import { useNavigate, useLocation } from 'react-router-dom'


interface TradePageProps {
  baseCoin: string
}



const TradePage = ({ baseCoin }: TradePageProps) => {

  const { openOrders, positions, availableFund, symbolListCtxs } = useWebData2()
  const { fills } = useUserFillsData()
  const allMeta = useAppSelector(selectAllPerpMeta)


  const navigate = useNavigate()
  const location = useLocation()

  const dispatch = useAppDispatch();


  const currentOpenOrders = useMemo(() => {
    return openOrders.map((item: xOpenOrders) => {
      const origSz = parseFloat(item.origSz || '0')
      const sz = parseFloat(item.sz || '0')

      if (origSz === 0) {
        const filterFills = fills.filter((citem: any) => citem.oid === item.oid)
        const totalFilled = filterFills.reduce((acc, citem) => {
          return acc + parseFloat(citem.sz || '0')
        }, 0)

        const positionItem = positions.find((citem: xPositions) => citem.coin === item.coin)
        const newOrigSz = positionItem?.szi ?? item.origSz

        return {
          ...item,
          origSz_1: newOrigSz,
          completedSz: totalFilled,
        }
      } else {
        return {
          ...item,
          origSz_1: origSz,
          completedSz: origSz - sz,
        }
      }
    })
  }, [fills, openOrders, positions])

 
  const currentListTabs: UITab[] = useMemo((): UITab[] => ([
    {
      value: 'order',
      label: `当前委托(${currentOpenOrders.length})`,
    },
    {
      value: 'position',
      label: `我的持仓(${positions.length})`,
    },
    {
      value: 'history',
      label: '成交历史',
    },
  ]), [currentOpenOrders.length, positions.length])

  const query = useMemo(() => new URLSearchParams(location.search), [location.search])
  const rawTab = query.get('tab')
  const validTabValues = useMemo(() => currentListTabs.map(tab => tab.value), [currentListTabs])
  const initialTab = validTabValues.includes(rawTab || '') ? rawTab! : currentListTabs[1].value


  const [currentTab, setCurrentTab] = useState<string>(initialTab)

  const [nextFunding, setNextFunding] = useState<PredictedFunding>({
    fundingRate: '',
    nextFundingTime: 0,
  })





  const handleChangeTab = (tab: string) => {
    setCurrentTab(tab)
    const searchParams = new URLSearchParams(location.search)
    searchParams.set('tab', tab)
    navigate({ search: searchParams.toString() }, { replace: true })
  }

  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case currentListTabs[1].value:
        return <MyPositionList baseCoin={baseCoin} positions={positions} />
      case currentListTabs[2].value:
        return <OrderHistoryList baseCoin={baseCoin}  />
      default:
        return <CurrentOrderList baseCoin={baseCoin} orders={currentOpenOrders} />
    }
  }

  const fetchNextPredictedFunding = async () => {
    try {
      const data = await getPredictedFundings()

      if (data?.length) {
        const item = data.find((item: any) => {
          return item[0] === baseCoin
        })
        const hourTs = 60 * 60 * 1000
        const fundingTimeItem = item?.[1]?.find((item: any) => {
          return item[0] === "HlPerp"
        })
        fundingTimeItem[1].nextFundingTime = fundingTimeItem[1].nextFundingTime + hourTs
        setNextFunding(fundingTimeItem[1])
      }
    } catch (err: any) {
    }
  }

  useEffect(() => {
      dispatch(setSymbolListCtxs(symbolListCtxs))
  }, [symbolListCtxs, dispatch])

 useEffect(() => {
    dispatch(updateUserFunding({ available: availableFund }))
  }, [availableFund, dispatch])



  useEffect(() => {
    fetchNextPredictedFunding()
  }, [baseCoin])

  return (
    <OpenOrdersContext.Provider value={currentOpenOrders} >
      <DetailSymbol  type="trade"/>
      <Chart baseCoin={baseCoin} disabledExpand={true} isTrendPage={false} positions={positions}/>
      <Container
        className="pt-[14px] rounded-tl-[8px] rounded-tr-[8px] pb-3
          border-t border-solid"
      >
        <HeaderSetting fundingRate={{
          fundingRate: nextFunding?.fundingRate,
          nextFundingTimeStr: <FeeCountdown targetTime={nextFunding?.nextFundingTime} />
        }} />
        <div className="flex gap-2">
          <div className="w-[37%] flex flex-col">
            <OrderBook />
            {/* <LastestTarde/> */}
            <LeftSetting />
          </div>
          <OrderForm baseCoin={baseCoin} containerClassName="w-[63%]" />
        </div>
      </Container>
      <div className="relative">
        <MovingLineTabs
          tabs={currentListTabs}
          onTabChange={handleChangeTab}
          defaultTab={initialTab}
          containerClassName="bg-[none] w-full mb-[10px]"
          tabsClassName="w-full"
        />

        {/* <Button
          variant={'ghost'}
          className="absolute top-[50%] transform -translate-y-1/2 right-[10px] py-1 px-2.5 rounded-[200px] text-[calc(12rem/16)] text-[#FFFFFF] bg-[#ECECED1F] h-[calc(22rem/16)] "
        >
          更多
        </Button> */}
      </div>

      {handleRenderTab(currentTab)}
    </OpenOrdersContext.Provider>

    
  )
}



export default TradePage
