export function formatNumber(num: number | string, decimal = 2) {
  if (num === undefined || num === null) return '--'
  if (typeof num === 'string') {
    num = parseFloat(num)
  }
  if (isNaN(num)) return '--'
  if (num === 0) return '0'
  const absNum = Math.abs(num)
  if (absNum < 1e3) {
    return num.toFixed(decimal)
  } else if (absNum < 1e6) {
    return `${(num / 1e3).toFixed(decimal)}K`
  } else if (absNum < 1e9) {
    return `${(num / 1e6).toFixed(decimal)}M`
  } else if (absNum < 1e12) {
    return `${(num / 1e9).toFixed(decimal)}B`
  } else {
    return `${(num / 1e12).toFixed(decimal)}T`
  }
}


export function formatPercent(num: number | string, decimal = 2, defaultValue = '--', isShowUnit = true, unit = '%') {
  if (num === undefined || num === null || num == 'Infinity' || num == '-Infinity') return defaultValue
  if (typeof num === 'string') {
    num = parseFloat(num)
  }
  if (isNaN(num)) return defaultValue
  const _num = Math.abs(num);
  //remove 12/06/2025: check negative/positive in outside function
  // const _character = _num ? num > 0 ? '+' : '-' : '';
  // if (_num > 1e5) return `${_character}99999${isShowUnit ? unit : ''}`;
  if (_num > 1e5) return `99999${isShowUnit ? unit : ''}`;
  let numStr = num.toFixed(decimal)
  if (numStr.indexOf('.00') > -1) {
    numStr = numStr.replace('.00', '')
  }
  return `${formatNumber(numStr)}${isShowUnit ? unit : ''}`
}

export function checkIsNumber(value: number | string): boolean {
  if (typeof value === 'number') {
    return !isNaN(value) && isFinite(value);
  }
  const parsedValue = parseFloat(value);
  return !isNaN(parsedValue) && isFinite(parsedValue);
}

