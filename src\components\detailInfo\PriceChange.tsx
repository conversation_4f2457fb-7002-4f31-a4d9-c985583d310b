import { useEffect, useMemo, useState } from 'react'
import eventBus from '@/lib/eventBus.ts'
import { EVENT_MESSAGE_OHLC_UPDATED } from '@/datafeeds/dataUpdater.ts'
import { formatLongValue } from '@/utils/helpers.ts'
import { useApolloClient } from '@apollo/client'
import { getTokenDetail } from '@services/tokens.service.ts'

export interface PriceChangeProps {
  tokenAddress?: string
  initialPrice?: number
  currentPrice?: number
  openTime24h?: number
}

export const PriceChange = (props: PriceChangeProps) => {
  const { initialPrice, currentPrice, openTime24h } = props
  const [ohlcToken, setOhlcToken] = useState<any | null>()
  const client = useApolloClient()

  useEffect(() => {
    eventBus.on(EVENT_MESSAGE_OHLC_UPDATED, (data: any) => {
      if (data?.data) {
        setOhlcToken(data?.data)
      }
    })
    return () => {
      eventBus.remove(EVENT_MESSAGE_OHLC_UPDATED)
    }
  }, [])

  useEffect(() => {
    const iv = setInterval(() => {
      const now = Date.now()
      if (openTime24h && now - openTime24h > 24 * 60 * 60 * 1000) {
        client.refetchQueries({
          include: [getTokenDetail],
        })
      }
    }, 60000)
    return () => {
      clearInterval(iv)
    }
  }, [openTime24h])

  const price24hChange = useMemo(() => {
    const price = ohlcToken ? (ohlcToken.close as number) : currentPrice
    if (!price || !initialPrice) return undefined
    return ((price - initialPrice) / initialPrice) * 100
  }, [initialPrice, currentPrice])

  const formattedPrice24hChange = useMemo(() => {
    if (!price24hChange) return '--'
    return `${Number(price24hChange) > 0 ? '+' : ''}${formatLongValue(Number(price24hChange), true)}%`
  }, [price24hChange])

  return (
    <div className="flex items-center gap-1.5 app-font-medium text-[calc(1rem*(12/16))] leading-[1] whitespace-nowrap mt-[6.5px]">
      <span className="text-[#FFF]/70">24h</span>
      {<span className={Number(price24hChange ?? 0) >= 0 ? 'text-rise' : 'text-fall'}>{formattedPrice24hChange}</span>}
    </div>
  )
}
