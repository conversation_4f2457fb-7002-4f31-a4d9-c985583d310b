import PositionLever from './PositionLever'
import PositionMode from './PositionMode'
import FundingFeeDesc from './FundingFeeDesc'
import { ReactNode, useMemo, useState, useEffect } from 'react'
import { WsActiveAssetCtx } from '@/types/hyperliquid'
import { RootState, useAppSelector, useAppDispatch } from '@/redux/store'
import { futuresTradeConfigActions, futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import { baseCoinSelector } from '@/redux/modules/futuresCurrentSymbol.slice'
import { PositionModeValue } from './types'
import { useIsLoggedInOnArb } from '@/hooks/hyperliquid/useIsLoggedInOnArb'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { mutationUserSymbolPreference } from '@/services/symbol.dex.service'
import { userGqlClient } from '@/lib/gql/apollo-client'
import { signHyperLiquidUpdateLeveragerMutation } from '@services/auth.service.ts'
import { toast } from 'sonner'
import { selectAllPerpMeta, selectMaxLeverageBySymbol } from '@/redux/modules/futuresMeta.slice'
import { isHyperOrderSuccess } from './tools'
import { Configs } from '@/const/configs'
import { privateKeyToAccount } from 'viem/accounts'
import { signStandardL1Action } from '../hooks/signing'
import { agentWalletSelector } from '@/redux/modules/futuresUserInfo.slice'


import { useToast } from '@/components/futuresDetails/tokenSearchDrawer/CustomToast'








interface FundingRate {
	fundingRate: string
	nextFundingTimeStr: string | ReactNode,
}


interface HeaderSettingProps {
	fundingRate: FundingRate
}
interface UpdateUserSymbolPreferenceParams {
	leverage: number,
	isCross: boolean
}

const HeaderSetting = ({ fundingRate }: HeaderSettingProps) => {
	const [isOpenFundingFeeDesc, setIsOpenFundingFeeDesc] = useState<boolean>(false)
  const { showToast } = useToast()
	const baseCoin = useAppSelector(baseCoinSelector)
	const tradeConfigs = useAppSelector(futuresTradeConfigSelector(baseCoin))
	const maxLeverage = useAppSelector(selectMaxLeverageBySymbol(baseCoin)) || ''

	const allMeta = useAppSelector(selectAllPerpMeta)

	const agentWallet = useAppSelector(agentWalletSelector)


	const isLogin = useIsLoggedInOnArb()


	const dispatch = useAppDispatch()


	const updateUserSymbolPreference = async ({ leverage, isCross }: UpdateUserSymbolPreferenceParams) => {
		const coinIndex = allMeta.findIndex((item: any) => baseCoin === item.name)
		const nonce = Date.now()
		const orderAction = {
			type: "updateLeverage",
			asset: coinIndex,
			isCross,
			leverage,
		}


		const wallet = privateKeyToAccount(agentWallet.privateKey)
		const signature = await signStandardL1Action(orderAction, wallet, null, nonce)

		const response = await fetch(`${Configs.getHyperliquidConfig().apiUrl}/exchange`, {
			method: 'POST',
			headers: { 'Content-Type': 'application/json' },
			body: JSON.stringify({
				action: orderAction,
				nonce: nonce,
				signature: signature,
				vaultAddress: null,
			}),
		})

		const result = await response.json()
		if (result.status === 'err') {
       showToast({
          type: 'error',
          title: result.response,
        })
			return
		}




		const { data } = await symbolDexClient.mutate({
			mutation: mutationUserSymbolPreference,
			variables: {
				input: {
					symbol: baseCoin,
					isCross,
					leverage
				}
			},
		})
		if (data?.updateUserSymbolPreference) {
			
			const { leverage, isCross, isFavorite } = data.updateUserSymbolPreference
			dispatch(futuresTradeConfigActions.updateFuturesTradeConfig({
				symbol: baseCoin, config: {
					positionMode: isCross ? 'cross' : 'isolated',
					leverage: leverage.toString(),
					isFavorite
				}
			}))
		}
    showToast({
      type: 'success',
      title: '修改成功',
    })
		


	}

	const handleModeChange = (mode: PositionModeValue) => {
		if (mode === tradeConfigs.positionMode) return
		if (isLogin) {
			updateUserSymbolPreference({
				leverage: Number(tradeConfigs.leverage),
				isCross: mode === 'cross' ? true : false
			})
		}
	}

	const handleLeverageChange = (leverage: string) => {
		// 如果当前设置杠杆大于最大杠杆，则不进行更新
		if (Number(leverage) > Number(maxLeverage)) {
			toast.error(`Invalid leverage value, the maximum supported leverage is ${maxLeverage} times`)
			return
		}
		// 如果杠杆值相同，则不进行更新
		if (leverage === tradeConfigs.leverage) return
		dispatch(futuresTradeConfigActions.updateFuturesTradeConfig({
			symbol: baseCoin,
			config: {
				leverage: leverage
			}
		}))
		if (isLogin) {
			updateUserSymbolPreference({
				isCross: tradeConfigs.positionMode === 'cross' ? true : false,
				leverage: Number(leverage)
			})
		}
	}

	const formattedFundingRate = useMemo(() => {
		const rate = parseFloat(fundingRate.fundingRate)
		return isNaN(rate) ? '--' : (rate * 100).toFixed(4)
	}, [fundingRate.fundingRate])

	  useEffect(() => {

	if (tradeConfigs.leverage === '' ) {
			dispatch(futuresTradeConfigActions.updateFuturesTradeConfig({ symbol: baseCoin, config: { 
				leverage: maxLeverage ? maxLeverage.toString() : ''
			} }))
		} 
	}, [maxLeverage, baseCoin, dispatch])




	return (
		<div className="flex items-center justify-between mb-4">
			<div>
				<div onClick={() => { setIsOpenFundingFeeDesc(true) }} className="text-[#FFFFFF80] text-[calc(12rem/16)] leading-[calc(12rem/16)] mb-1.5">
					资金费率<span className="text-[calc(8rem/16)] leading-[calc(8rem/16)]">/</span>倒计时
				</div>

				<div className="leading-[calc(12rem/16)] flex items-center">
					<span className="text-rise text-[calc(12rem/16)] ">{formattedFundingRate}%</span>
					<span className="mx-0.5 text-[calc(9rem/16)] leading-[calc(9rem/16)]">/</span>
					<span className="text-[calc(11rem/16)] leading-[calc(11rem/16)]">{fundingRate.nextFundingTimeStr}</span>
				</div>
			</div>

			<div className='flex items-center gap-1.5'>
				<PositionMode
					mode={tradeConfigs.positionMode}
					onChange={(mode: PositionModeValue) => { handleModeChange(mode) }}
				/>
				<PositionLever
					leverage={tradeConfigs.leverage}
					onChange={(value: string) => { handleLeverageChange(value) }}

				/>
			</div>

			<FundingFeeDesc isOpen={isOpenFundingFeeDesc} onOpenChange={(status) => { setIsOpenFundingFeeDesc(status) }} />



		</div>
	)
}

export default HeaderSetting