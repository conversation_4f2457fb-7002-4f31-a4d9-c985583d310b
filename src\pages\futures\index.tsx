import BtcVault from '@/components/futuresDetails/btcVault'
import { setSymbolInfo, resetSymbolInfo } from '@/redux/modules/futuresCurrentSymbol.slice'
import { updateFuturesTradeConfig, futuresTradeConfigSelector } from '@/redux/modules/futuresTradeConfigs.slice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { UITab } from '@/types/uiTabs.ts'
import Container from '@components/common/Container.tsx'
import MovingChangeBgTabs from '@components/common/MovingChangeBgTabs.tsx'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import TradePage from '@components/futuresDetails/trade/index'
import TrendPage from '@components/futuresDetails/trend/index'
import { useEffect, useRef, useState, useMemo, useCallback, memo, use } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams, useNavigate } from 'react-router-dom'
import DetailHeaderButton from '@/components/futuresDetails/DetailHeaderButton'
import { getPerpMetaAndAssetCtxs } from '@/api/hyperliquid'
import { setUniverse, setAllTiers, selectMaxLeverageBySymbol } from '@/redux/modules/futuresMeta.slice'
import { generateL2BookTiers } from '@/components/futuresDetails/trade/tools'
import { orderButtonStatusSelector } from '@/redux/modules/futuresUserInfo.slice'
import TopInfo from '@/components/futuresDetails/trade/TopInfo.tsx'
import { GET_SYMBOL_LIST, getUserSymbolPreference } from '@/services/symbol.dex.service'
import { symbolDexClient } from '@/lib/gql/apollo-client'
import { useIsLoggedInOnArb } from '@/hooks/hyperliquid/useIsLoggedInOnArb'
import {
  futuresTradePreferencesActions,
  selectFuturesTradePreferences,
} from '@/redux/modules/futuresTradePreferences.slice'
import { ISymbolList } from '../futures-market/type'
import { ToastProvider } from '@/components/futuresDetails/tokenSearchDrawer/CustomToast'



const navTabs: UITab[] = [
  {
    value: 'trading',
    label: '合约',
  },
  /* {
    value: 'btc gold',
    label: 'BTC金库',
  },
  {
    value: 'smart',
    label: '聪明钱',
  }, */
]

const typeList: UITab[] = [
  {
    value: 'trend',
    label: '走势图',
  },
  {
    value: 'trade',
    label: '交易',
  },
]

const DEFAULT_FUTURES_BASE_COIN = import.meta.env.VITE_DEFAULT_FUTURES_BASE_COIN
const FuturesDetailPage = () => {
  const { t } = useTranslation()
  const { baseCoin = DEFAULT_FUTURES_BASE_COIN } = useParams()
  const navigate = useNavigate()
  const upperCaseBaseCoin = baseCoin.toUpperCase()
  const { allSymbols } = useAppSelector(selectFuturesTradePreferences)

  const dispatch = useAppDispatch()

  const currentBaseCoin = useAppSelector((state) => state.futuresCurrentSymbol.baseCoin)

  const orderButtonStatus = useAppSelector(orderButtonStatusSelector)

  const isLogin = useIsLoggedInOnArb()

  const [showNavTabs, setShowNavTabs] = useState(true)
  const [headerOpacity, setHeaderOpacity] = useState(1)
  const [currentNavTab, setCurrentNavTab] = useState<string>(navTabs[0].value)
  const [currentTypeTab, setCurrentTypeTab] = useState<string>(typeList[1].value)

  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const lastScrollY = useRef(0)
  const scrollTimeoutRef = useRef<number | undefined>(undefined)

  const handleCheckCorrectSymbolWithRouter = () => {
    if (!allSymbols || !allSymbols.length) return

    const regex = new RegExp(`^${baseCoin}$`, 'i')
    const symbolIndex = allSymbols.findIndex((item) => regex.test(item))

    if (symbolIndex === -1) {
      navigate('/futures', { replace: true })
      return
    }

    const correctSymbol = allSymbols[symbolIndex]

    if (baseCoin !== correctSymbol) {
      const currentPath = location.pathname
      const targetPath = currentPath.replace(`/${baseCoin}`, `/${correctSymbol}`)
      navigate(targetPath, { replace: true })
    }
  }

  useEffect(() => {
    const handleGetSymbolList = async () => {
      try {
        const input = {
          condition: 'volume',
        }
        const { data } = await symbolDexClient.query({
          query: GET_SYMBOL_LIST,
          variables: { input },
        })
        dispatch(
          futuresTradePreferencesActions.updateAllSymbols(
            data?.getSymbolList?.list.map((item: ISymbolList) => item.symbol) || [],
          ),
        )

      } catch (error) {
        console.error('Error fetching symbol list:', error)
      }
    }

    if (!allSymbols || !allSymbols.length) {
      handleGetSymbolList()
    }
  }, [])

  useEffect(() => {
    handleCheckCorrectSymbolWithRouter()
  }, [baseCoin, allSymbols])

  const fetchPerpMetadata = async () => {
    try {
      const data = await getPerpMetaAndAssetCtxs()

      const universe = data[0]?.universe
      const contexts = data[1]

      const allTiers: any = {}

      if (universe.length) {
        for (let i = 0; i < universe.length; i++) {
          const coin = universe[i].name
          const szDecimals = universe[i].szDecimals
          const ctx = contexts[i]
          const rawMid = ctx.midPx ?? ctx.markPx
          if (!rawMid) continue
          const price = parseFloat(rawMid)
          allTiers[coin] = generateL2BookTiers(coin, price, szDecimals)
        }
        dispatch(setAllTiers(allTiers))

        dispatch(setUniverse(universe));
        const { szDecimals, maxLeverage, marginTableId } = universe.find((item:any) => {
          return item.name === baseCoin
        })
        dispatch(setSymbolInfo({ szDecimals, maxLeverage, marginTableId }))
      }
    } catch (err: any) {}
  }

  const fetchUserSymbolPreference = async () => {
    const { data } = await symbolDexClient?.query<any>({
      query: getUserSymbolPreference,
      variables: {
        input: {
          symbol: baseCoin
        }
      },
    })
    if (data?.getUserSymbolPreference) {
      const { leverage, isCross, isFavorite } = data.getUserSymbolPreference
      dispatch(
        updateFuturesTradeConfig({
          symbol: baseCoin,
          config: {
            positionMode: isCross ? 'cross' : 'isolated',
            leverage: leverage.toString(),
            isFavorite,
          },
        }),
      )
    }
  }

  useEffect(() => {
    if (baseCoin && currentBaseCoin !== baseCoin) {
      dispatch(setSymbolInfo({ baseCoin: baseCoin }))
      dispatch(updateFuturesTradeConfig({ symbol: baseCoin, config: {} }))
    }
    return () => {
      dispatch(resetSymbolInfo())
    }
  }, [baseCoin, currentBaseCoin, dispatch])
  
  useEffect(() => {
  if (!baseCoin) return;
  fetchPerpMetadata();

  if (isLogin) fetchUserSymbolPreference()
}, [baseCoin, isLogin]);

  // 监听 currentTypeTab 变化，页面回到顶部
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    }
  }, [currentTypeTab])

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current
    if (!scrollContainer) return

    const handleScroll = (e: Event) => {
      // Only handle scroll events from the main scroll container, not child elements
      if (e.target !== scrollContainer) return

      // Debounce scroll events
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }

      scrollTimeoutRef.current = window.setTimeout(() => {
        const mainHeader = document.getElementById('futures-main-header')
        const typeHeader = document.getElementById('futures-type-header')

        if (!mainHeader || !typeHeader) return

        const mainHeaderRect = mainHeader.getBoundingClientRect()
        const typeHeaderRect = typeHeader.getBoundingClientRect()
        const currentScrollY = scrollContainer.scrollTop

        // Calculate opacity based on scroll position
        const maxScroll = 50 // Point at which header becomes fully transparent
        const opacity = Math.max(0, 1 - currentScrollY / maxScroll)
        setHeaderOpacity(opacity)

        // If type header is close to or overlapping with main header, hide main header
        if (typeHeaderRect.top <= mainHeaderRect.height + 10 && currentScrollY > 50) {
          setShowNavTabs(false)
        } else if (currentScrollY <= 10) {
          setShowNavTabs(true)
        }

        lastScrollY.current = currentScrollY
      }, 10)
    }

    scrollContainer.addEventListener('scroll', handleScroll, { passive: true })
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
      scrollContainer.removeEventListener('scroll', handleScroll)
    }
  }, [currentTypeTab])

  const handleRenderTab = useCallback(
    (tab: string) => {
      return <RenderNavTab />
      /* switch (tab) {
      case navTabs[1].value:
        return <BtcVault />
      default:
        return <RenderNavTab />
    } */
  }, [currentTypeTab, baseCoin])


  const handleNavigateToTrade = () => {
    setCurrentTypeTab(typeList[1].value) // 切换到交易tab

    // 页面回到顶部
    const scrollContainer = scrollContainerRef.current
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    }
  }

  // const handleRenderType = (type: string) => {
  //   switch (type) {
  //     case typeList[1].value:
  //       return <TradePage baseCoin={baseCoin} />
  //     default:
  //       return <TrendPage baseCoin={baseCoin} onNavigateToTrade={handleNavigateToTrade} />
  //   }
  // }
  // Create a memoized component for the chart content
  const ChartContent = memo(
    ({ type, baseCoin, onNavigateToTrade }: { type: string; baseCoin: string; onNavigateToTrade: () => void }) => {
      return type === typeList[1].value ? (
        <TradePage baseCoin={baseCoin} />
      ) : (
        <TrendPage baseCoin={baseCoin} onNavigateToTrade={onNavigateToTrade} />
      )
    },
  )

  const RenderNavTab = () => {
    return (
      <div className="min-h-full">
        <div
          id="futures-type-header"
          className="sticky bg-black transition-all pt-[10px] w-full z-[10]"
          style={{
            top: '0',
          }}
        >
          <Container>
            <MovingChangeBgTabs
              containerId="detail-type-tabs"
              tabs={typeList}
              defaultTab={currentTypeTab}
              onTabChange={(tab: string) => {
                setCurrentTypeTab(tab)

                // 页面回到顶部
                const scrollContainer = scrollContainerRef.current
                if (scrollContainer) {
                  scrollContainer.scrollTo({
                    top: 0,
                    behavior: 'smooth',
                  })
                }
              }}
              tabBgClassName="!bg-[rgba(0,255,180,0.2)] rounded-[12px] h-[28px]"
              tabsListClassName="w-full mb-2"
              containerClassName=""
              tabsTriggerClassName="flex-1"
            />
          </Container>
        </div>

        {/* {handleRenderType(currentTypeTab)} */}
        <div 
          className={`transition-all duration-300 pb-[100px] `}
        >
          <ChartContent type={currentTypeTab} baseCoin={baseCoin} onNavigateToTrade={handleNavigateToTrade} />
        </div>
      </div>
    )
  }

  return (
    <ToastProvider>
    <div className="relative h-screen flex flex-col overflow-hidden">
      <div
        id="futures-main-header"
        className={`w-full fixed top-0 bg-black transition-all duration-300 z-20 border-b-1 border-b-[#ECECED14] ${
          showNavTabs ? 'h-[50px] translate-y-0' : 'h-0 -translate-y-full'
        }`}
        style={{
          visibility: showNavTabs ? 'visible' : 'hidden',
          opacity: showNavTabs ? headerOpacity : 0,
          transition: 'all 0.3s ease-in-out',
          maxWidth: '768px',
          left: '50%',
          transform: showNavTabs ? 'translateX(-50%) translateY(0)' : 'translateX(-50%) translateY(-100%)',
        }}
      >
        {/* <DetailHeader /> */}
        <div className="flex items-center justify-between h-full">
          <MovingLineTabs
            tabs={navTabs}
            defaultTab={navTabs[0].value}
            // disabledTabs={navTabs.filter((tab) => tab.value === navTabs[2].value).map((tab) => tab.value)}
            onTabChange={(tab: string) => {
              setCurrentNavTab(tab)
            }}
            containerClassName="after:hidden bg-[none] w-full border-b border-solid border-[#ECECED14]"
            tabsClassName="w-full"
          />
          <DetailHeaderButton token={baseCoin}/>
        </div>
      </div>

      {orderButtonStatus === 'toggle' && <TopInfo text="当前连接的钱包不支持Arbitrum网络,无法交易合约,请切换钱包" />}
      <div
        className="w-full overflow-y-auto _hidescrollbar"
        ref={scrollContainerRef}
        style={{
          // maxHeight: 'calc(100vh - 190px)',
          overscrollBehavior: 'contain',
          paddingTop: showNavTabs ? '50px' : '0',
        }}
      >
        {handleRenderTab(currentNavTab)}
      </div>
    </div>
    </ToastProvider>
  )
}

export default FuturesDetailPage
