import { Notification } from '@/@generated/gql/graphql-notification.ts'
import dayjs from 'dayjs'
import 'dayjs/locale/zh'
import 'dayjs/locale/en'
import 'dayjs/locale/hi'
import 'dayjs/locale/ja'
import { useTranslation } from 'react-i18next'
import { APP_PATH } from '@/lib/constant.ts'
import { useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { notificationClient } from '@/lib/gql/apollo-client.ts'
import { readNotification } from '@services/notifications.service.ts'
import { cn } from '@/lib/utils.ts'
import { Ref, useMemo } from 'react'

export interface NotificationItemProps {
  notification: Notification
  ref?: Ref<HTMLDivElement>
}

const formatCreatedAt = (createdAt: string, language: string) => {
  const now = dayjs()
  const date = dayjs(createdAt)
  if (now.diff(date, 'hour') < 6) {
    return date.locale(language).fromNow()
  } else if (now.diff(date, 'hour') < 24) {
    return date.locale(language).format('HH:mm')
  } else {
    return date.locale(language).format('YYYY/MM/DD HH:mm')
  }
}

const useReadNotification = () => {
  const { mutate: markRead } = useMutation({
    mutationFn: async (notificationId: string) => {
      await notificationClient.mutate({
        mutation: readNotification,
        variables: { id: notificationId },
      })
    },
  })
  return markRead
}

const ViewButton = (props: { type: string }) => {
  const { type } = props
  const { t } = useTranslation()
  const viewText = useMemo(() => {
    if (type === 'SmartMoneyActivity') {
      return t('notifications.viewSmartMoney')
    }
    if (type === 'Others') {
      return t('notifications.viewOnExplorer')
    }
  }, [type])
  return (
    <div className="text-[#00ffb4] flex items-center gap-1 text-[calc(12rem/16)] leading-[calc(12rem/16)] font-medium">
      {viewText}
      <svg
        width="25"
        height="24"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="size-3 translate-y-[1px]"
      >
        <path
          d="M14.9302 5.93018L21.0002 12.0002L14.9302 18.0702"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4 12H20.83"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  )
}

export const NotificationItem = (props: NotificationItemProps) => {
  const { notification, ref } = props
  const { i18n } = useTranslation()
  const navigate = useNavigate()
  const markRead = useReadNotification()

  const handleClick = () => {
    if (!notification.read) {
      markRead(notification.id)
    }
    if (notification.type === 'SmartMoneyActivity') {
      const metadata = notification.metadata ? JSON.parse(notification.metadata) : {}
      const url = APP_PATH.MEME_ASSETS_WALLET + '/' + metadata.wallet_address
      navigate(url, {
        state: {
          fromNotification: true,
          notificationId: notification.id,
        },
      })
    }
    if (notification.type === 'Others') {
      const metadata = notification.metadata ? JSON.parse(notification.metadata) : {}
      const txHash = metadata.txid
      // TODO: temporary support only Solana chain
      const searchParams = new URLSearchParams()
      searchParams.set('page', 'overview')
      searchParams.set('tab', 'history')
      searchParams.set('tx', txHash)
      const url = APP_PATH.MEME_ASSETS + '?' + searchParams.toString()
      navigate(url, {
        state: {
          fromNotification: true,
          notificationId: notification.id,
        },
      })
    }
  }

  return (
    <div
      ref={ref}
      className={cn(
        'flex items-start p-4 rounded-lg shadow-md relative transition-all duration-300 hover:scale-[1.02] border border-gray-700 bg-gradient-to-r  cursor-pointer',
        notification.read
          ? 'from-[rgba(8,255,181,0.05)] to-[rgba(153,69,255,0.05)]'
          : 'from-[rgba(8,255,181,0.1)] to-[rgba(153,69,255,0.1)]',
      )}
      onClick={handleClick}
    >
      <div className="flex-shrink-0 mr-2 md:mr-4">
        <div className="w-11 h-11 rounded-full flex items-center justify-center bg-gradient-to-bl bg-[#12191D]">
          <img
            src={notification.avatar || '/images/icons/icon-fallback.svg'}
            alt=""
            className="w-10 h-10 rounded-full object-cover"
          />
        </div>
      </div>
      <div className="flex-1">
        <div className="flex justify-between items-start mb-1">
          <div className="flex items-center">
            <h3 className="font-medium text-[calc(14rem/16)] text-white">{notification.title}</h3>
          </div>
          <div className="flex items-center gap-2">
            {!notification.read && (
              <span className="w-2.5 h-2.5 bg-[#00ffb4] rounded-full ml-2 flex-shrink-0 animate-pulse"></span>
            )}
            <p className="text-gray-500 text-[calc(10rem/16)] leading-2.5 flex-shrink-0">
              {formatCreatedAt(notification.createdAt, i18n.language)}
            </p>
          </div>
        </div>
        <p className="text-gray-400 text-sm mb-2 w-full break-all">{notification.description}</p>
        <ViewButton type={notification.type} />
      </div>
    </div>
  )
}
