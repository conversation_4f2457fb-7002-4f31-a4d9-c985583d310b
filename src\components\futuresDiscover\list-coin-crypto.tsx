import { symbolDexClient } from '@/lib/gql/apollo-client'
import { useSearchFilter } from '@/pages/futures-market/hooks/useHandleGetData'
import useSymbolListSubscription, { useMergedData } from '@/pages/futures-market/hooks/useSymbolListSubscription'
import {
  futuresTradePreferencesActions,
  selectFuturesTradePreferences,
} from '@/redux/modules/futuresTradePreferences.slice'
import { useAppDispatch, useAppSelector } from '@/redux/store'
import { GET_SYMBOL_LIST } from '@/services/symbol.dex.service'
import { UITab } from '@/types/uiTabs'
import React, { useEffect, useState } from 'react'
import CryptoList from './crypto-list'
import CryptoTable from './table/crypto-table'

const HEADER_TABS: UITab[] = [
  { value: '自选', label: '自选' },
  { value: '热门', label: '热门' },
  { value: '涨幅榜', label: '涨幅榜' },
  { value: '跌幅榜', label: '跌幅榜' },
  { value: '成交额', label: '成交额' },
  { value: '持仓额', label: '持仓额' },
]

export interface ISymbolList {
  changPxPercent: number
  currentPrice: number
  marketCap: number
  maxLeverage: number
  symbol: string
  volume: string
}

const TabContent = React.memo(
  ({
    currentTab,
    data,
    isLoading,
    total,
  }: {
    currentTab: string
    data: ISymbolList[]
    isLoading: boolean
    total: number
  }) => {
    switch (currentTab) {
      case HEADER_TABS[1].value:
        return <CryptoTable initialData={data} isLoading={isLoading} total={total} />
      default:
        return <CryptoTable initialData={data} isLoading={isLoading} total={total} />
    }
  },
)

const ListCoinCrypto = ({ search }: { search: string }) => {
  const [symbolList, setSymbolList] = useState<ISymbolList[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const filteredData = useSearchFilter(symbolList || [], search)
  const [total, setTotal] = useState(0)
  const dispatch = useAppDispatch()
  const { allSymbols } = useAppSelector(selectFuturesTradePreferences)

  const { symbolData } = useSymbolListSubscription({
    shouldSkip: false,
  })
  const currentData = useMergedData(filteredData, symbolData)

  useEffect(() => {
    const handleGetSymbolList = async () => {
      try {
        const input = {
          condition: 'volume',
        }
        const { data, loading } = await symbolDexClient.query({
          query: GET_SYMBOL_LIST,
          variables: { input },
        })
        if (allSymbols.length === 0)
          dispatch(
            futuresTradePreferencesActions.updateAllSymbols(
              data?.getSymbolList?.list.map((item: ISymbolList) => item.symbol) || [],
            ),
          )
        setSymbolList(data?.getSymbolList?.list.slice(0, 10) || [])
        setTotal(data?.getSymbolList?.list.length || 0)
        setIsLoading(loading)
      } catch (error) {
        console.error('Error fetching symbol list:', error)
      } finally {
        setIsLoading(false)
      }
    }

    handleGetSymbolList()
  }, [])

  return (
    <div className="">
      <CryptoList initialData={currentData} isLoading={isLoading} total={total} />
    </div>
  )
}

export default React.memo(ListCoinCrypto)
