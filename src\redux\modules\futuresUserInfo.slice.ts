import { createSlice, PayloadAction } from '@reduxjs/toolkit'

export type OrderButtonStatus = 'connect' | 'toggle' | 'login' | 'deposit' | 'approve' | 'order'

export interface BuilderInfo {
  b: string
  f: number
}

export interface AgentWalletInfo {
  address: string
  privateKey: string
}

export interface FuturesUserInfoState {
  funding: {
    available: number
  }
  isAuthorized: boolean
  isDeposit: boolean
  orderButtonStatus: OrderButtonStatus
  builder: BuilderInfo
  agentWallet: AgentWalletInfo | null
}

export const initialStateUserInfo: FuturesUserInfoState = {
  funding: {
    available: 0,
  },
  isAuthorized: false,
  isDeposit: false,
  orderButtonStatus: 'order',
  builder: {
    b: '',
    f: 0,
  },
  agentWallet: null,
}

export const futuresUserInfoSlice = createSlice({
  name: 'futuresUserInfo',
  initialState: initialStateUserInfo,
  reducers: {
    updateUserFunding: (
      state,
      action: PayloadAction<Partial<FuturesUserInfoState['funding']>>
    ) => {
      Object.assign(state.funding, action.payload)
    },

    updateAuthorizationStatus: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isAuthorized = action.payload
    },

    updateDepositStatus: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.isDeposit = action.payload
    },

    updateOrderButtonStatus: (
      state,
      action: PayloadAction<OrderButtonStatus>
    ) => {
      state.orderButtonStatus = action.payload
    },

    updateBuilderInfo: (
      state,
      action: PayloadAction<Partial<BuilderInfo>>
    ) => {
      Object.assign(state.builder, action.payload)
    },

    updateAgentWallet: (
      state,
      action: PayloadAction<AgentWalletInfo | null>
    ) => {
      state.agentWallet = action.payload
    },
  },
  extraReducers: (builder) => {},
})

export const {
  updateUserFunding,
  updateAuthorizationStatus,
  updateDepositStatus,
  updateOrderButtonStatus,
  updateBuilderInfo,
  updateAgentWallet,
} = futuresUserInfoSlice.actions

export const futuresUserInfoActions = {
  ...futuresUserInfoSlice.actions,
}

export const fundingSelector = (state: { futuresUserInfo: FuturesUserInfoState }) =>
  state.futuresUserInfo.funding

export const isAuthorizedSelector = (state: { futuresUserInfo: FuturesUserInfoState }) =>
  state.futuresUserInfo.isAuthorized

export const isDepositSelector = (state: { futuresUserInfo: FuturesUserInfoState }) =>
  state.futuresUserInfo.isDeposit

export const orderButtonStatusSelector = (state: { futuresUserInfo: FuturesUserInfoState }) =>
  state.futuresUserInfo.orderButtonStatus

export const builderSelector = (state: { futuresUserInfo: FuturesUserInfoState }) =>
  state.futuresUserInfo.builder

export const agentWalletSelector = (state: { futuresUserInfo: FuturesUserInfoState }) =>
  state.futuresUserInfo.agentWallet

export default futuresUserInfoSlice
