import Container from '@components/common/Container.tsx'
import Chart from '@components/chart/index.tsx'
import OrderForm from '@components/orderForm'
import MovingLineTabs from '@components/common/MovingLineTabs.tsx'
import { UITab } from '@/types/uiTabs.ts'
import DetailStatistic from '@components/detailStatistic'
import OrderBook from '@components/orderBook'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { useQuery } from '@apollo/client'
import { getTokenDetail } from '@/services/tokens.service'
import { useEffect, useRef, useState } from 'react'
import { TYPE_ACCOUNT, TYPE_CHAIN } from '@/lib/blockchain'
import WalletSolanaConnectModal from '@/components/header/wallet-connect'
import { RootState, useAppDispatch, useAppSelector } from '@/redux/store'
import { useTranslation } from 'react-i18next'
import CurrentOrdersList from '@/components/currentOrdersList'
import { setCurrentToken } from '@/redux/modules/holding.slice.ts'
import { APP_PATH, CHAIN_SYMBOLS } from '@/lib/constant'
import LastTransactionSubscription from '@components/LastTransactionSubscription.tsx'
import HoldingTab from '@components/myPositions/HoldingTab.tsx'
import { useMultiChainWallet } from '@/hooks/useMultiChainWallet'
import { ServiceConfig } from '@/lib/gql/service-config'
import DetailTokenTabs from '@components/detailTokenTabs'
import DetailListIcon from '@components/detailListIcon'
import { getPath } from '@/lib/utils.ts'
import DetailTokenTable from '@components/detaiTokenTable'
import DetailHolderTab from '@components/detailHolderTab'
import TokenPageTitle from '@components/TokenPageTitle'
import ButtonLogin from '@/components/common/LoginSection/ButtonLogin'
import useSignWallet from '@/hooks/useSignWallet'
import { browsingHistoryActions } from '@/redux/modules/browsingHistory.slice.ts'
import { countPendingOrders } from '@services/order.service.ts'
import { tradingClient } from '@/lib/gql/apollo-client.ts'
import eventBus from '@/lib/eventBus.ts'
import { REFETCH_PENDING_ORDERS } from '@/lib/eventMessages.ts'
import DetailPoolTab from '@/components/detailPoolTab'
import { setCurrentDetailTab, TradeTabState } from '@/redux/modules/tradeTab.slice.ts'
import { TabInfo } from '@components/memeDetail/TabInfo.tsx'
import { TabAI } from '@components/memeDetail/TabAI.tsx'
import { LoginDrawer } from '@/components/common/LoginDrawer'
import { DetailInfoWrapper } from '@components/detailInfo/DetailInfoWrapper.tsx'

const DEFAULT_MEME_TOKEN = import.meta.env.VITE_DEFAULT_MEME_TOKEN
const DEFAULT_MEME_TOKEN_CHAIN_ID = import.meta.env.VITE_DEFAULT_MEME_CHAIN_ID

const MemeDetailPage = () => {
  const { t } = useTranslation()

  const dispatch = useAppDispatch()
  const chain = useAppSelector((state) => state.wallet.activeChain)
  const { currentDetailTab } = useAppSelector((state: RootState) => state.tradeTab as TradeTabState)

  const activeAccount = useAppSelector((state) => state.wallet.activeAccount)
  const { address } = useParams()
  const navigate = useNavigate()
  const [showLoginDrawer, setShowLoginDrawer] = useState(false)
  const { activeWallet } = useMultiChainWallet({})
  const { handleSignMessage } = useSignWallet({ isAutoConnect: false })
  const retryCountRef = useRef(1)
  const MAX_RETRIES = 20
  const { data, refetch, error } = useQuery(getTokenDetail, {
    skip: !address,
    variables: {
      input: {
        address: address,
      },
    },
    fetchPolicy: 'no-cache',
  })
  const [retryTrigger, setRetryTrigger] = useState(0)
  useEffect(() => {
    if (error) {
      const errorCode = error?.graphQLErrors?.[0]?.extensions?.code
      if (errorCode === 'TOKEN_NOT_FOUND' && retryCountRef.current < MAX_RETRIES) {
        const delay = Math.pow(1.8, retryCountRef.current) * 1000
        console.warn(`Retrying #${retryCountRef.current + 1} in ${delay / 1000}s...`)
        const timer = setTimeout(() => {
          retryCountRef.current += 1
          refetch()
            .then((_) => {
              setRetryTrigger((v) => v + 1)
            })
            .catch((_) => {
              setRetryTrigger((v) => v + 1)
            })
        }, delay)
        return () => clearTimeout(timer)
      }
    }
  }, [error, retryTrigger])

  const userAddress = activeWallet?.walletId
  const { data: dataCountPendingOrders, refetch: reCountPendingOrders } = useQuery(countPendingOrders, {
    skip: !userAddress,
    client: tradingClient,
    variables: {
      input: {
        userAddress,
      },
    },
  })

  const pendingOrdersCount = dataCountPendingOrders?.getPendingOrders?.total
  const pendingOrdersCountString = pendingOrdersCount > 99 ? '99+' : pendingOrdersCount

  const navTabs: UITab[] = [
    {
      value: 'trading',
      label: t('detail.tabs.trading'),
    },
    {
      value: 'info',
      label: t('detail.tabs.information'),
    },
    {
      value: 'AI',
      label: t('detail.tabs.aiAnalysis'),
    },
  ]

  const numsHolders = 0

  const currentListTabs: UITab[] = [
    {
      value: 'followed',
      label: t('detail.tabs.followed'),
    },
    {
      value: 'trading',
      label: t('detail.tabs.trading'),
    },
    {
      value: 'holders',
      label: t('detail.tabs.holders', { total: numsHolders > 0 ? `(${numsHolders})` : '' }),
    },
    {
      value: 'pool',
      label: t('detail.tabs.pool'),
    },
    {
      value: 'order',
      label: pendingOrdersCount
        ? t('detail.tabs.currentCommissionWithCount', { total: pendingOrdersCountString })
        : t('detail.tabs.currentCommission'),
    },
    {
      value: 'holding',
      label: t('detail.tabs.myPositions'),
    },
  ]

  const [searchParams, setSearchParams] = useSearchParams()
  const [currentNavTab, setCurrentNavTab] = useState<string>(searchParams.get('page') || navTabs[0].value)
  const [currentTab, setCurrentTab] = useState<string>(
    searchParams.get('tab') || currentDetailTab || currentListTabs[5].value,
  )

  const handleChangeNavTab = (tab: string) => {
    setCurrentNavTab(tab)
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set('page', tab)
      return newParams
    })
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
  const handleChangeTab = (tab: string) => {
    setCurrentTab(tab)
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set('tab', tab)
      return newParams
    })
    dispatch(setCurrentDetailTab(tab))
  }
  const handleRenderTab = (tab: string) => {
    switch (tab) {
      case currentListTabs[0].value:
        return <DetailTokenTabs tokenData={tokenData} />
      case currentListTabs[1].value:
        return <DetailTokenTable symbol={tokenData?.symbol} price={tokenData?.price} />
      case currentListTabs[2].value:
        return <DetailHolderTab />
      case currentListTabs[3].value:
        return (
          <DetailPoolTab
            token={tokenData?.address}
            chainId={tokenData?.chainId}
            icon={tokenData?.info?.logoUrl}
            symbol={tokenData?.symbol}
          />
        )
      case currentListTabs[4].value:
        return <CurrentOrdersList currentToken={address} />
      case currentListTabs[5].value:
      default:
        return <HoldingTab />
    }
  }

  const hadleClickBtnLogin = () => {
    if (activeAccount === TYPE_ACCOUNT.CHAIN && activeWallet.isConnected && !ServiceConfig.token) {
      handleSignMessage()
      return
    }
    if (!activeWallet.isConnected) {
      setShowLoginDrawer(true)
    }
  }

  const ButtonConnectWallet = () => {
    return (
      <Container className="mt-[10px] h-40">
        <div className="flex items-center gap-2 flex-col justify-center text-[14px] text-[#999999] mt-10">
          <p>
            {t('login.notLogined', {
              name: 'XBIT',
            })}
          </p>
          <ButtonLogin onClick={hadleClickBtnLogin} className="hover-scale">
            <img src="/images/icons/icon-wallet.svg" className="w-[1rem] h-[calc(1rem*(13.43/16))]" alt="" />
            {t('wallet.connectGuide')}
          </ButtonLogin>
        </div>
        <LoginDrawer setOpen={setShowLoginDrawer} open={showLoginDrawer} />
      </Container>
    )
  }

  const tokenData = data?.getTokenDetail

  useEffect(() => {
    if (tokenData) {
      dispatch(setCurrentToken(tokenData?.address))
    }
  }, [tokenData])

  useEffect(() => {
    if (!address) {
      const recentToken = sessionStorage.getItem('recentToken')
        ? JSON.parse(sessionStorage.getItem('recentToken')!)
        : null
      if (recentToken) {
        navigate(getPath(APP_PATH.MEME_TOKEN_DETAIL, { address: recentToken.token, chain: recentToken.chain }))
        return
      }

      navigate(
        getPath(APP_PATH.MEME_TOKEN_DETAIL, {
          address: DEFAULT_MEME_TOKEN,
          chain: CHAIN_SYMBOLS[+DEFAULT_MEME_TOKEN_CHAIN_ID],
        }),
      )
      return
    }

    sessionStorage.setItem('recentToken', JSON.stringify({ token: address, chain: chain }))
  }, [address])

  useEffect(() => {
    if (!address) return

    // Save to history
    dispatch(browsingHistoryActions.addTokenToHistory(address))
  }, [address])

  useEffect(() => {
    if (!address) return

    // Save to history
    dispatch(browsingHistoryActions.addTokenToHistory(address))
  }, [address])

  useEffect(() => {
    eventBus.on(REFETCH_PENDING_ORDERS, (data: any) => {
      const needRefetch = data?.data?.needRefetch
      if (needRefetch) {
        reCountPendingOrders().catch(console.error)
      }
    })
    return () => {
      eventBus.remove(REFETCH_PENDING_ORDERS)
    }
  }, [])

  return (
    <>
      <LastTransactionSubscription baseAddress={address} />
      <TokenPageTitle address={address} symbol={tokenData?.symbol} defaultPrice={tokenData?.price} />
      <div className="relative">
        {/* sticky header*/}
        <div className="sticky top-0 z-20 bg-[#111111]">
          {chain === TYPE_CHAIN.SOLANA && <WalletSolanaConnectModal />}
          {/*<DetailHeader tokenData={tokenData} />*/}
          <div className="flex items-center justify-between w-full py-1.5 border-b-[0.6px] border-b-[#ECECED14]">
            <MovingLineTabs
              tabs={navTabs}
              defaultTab={currentNavTab}
              onTabChange={handleChangeNavTab}
              containerClassName="bg-[none] after:hidden"
              tabsClassName="w-full"
            />
            <DetailListIcon tokenData={tokenData} />
          </div>
        </div>
        <div id="tab-trading" className={currentNavTab === navTabs[0].value ? 'block' : 'hidden'}>
          <DetailInfoWrapper tokenData={tokenData} />
          <Chart tokenData={tokenData} />
          <DetailStatistic />
          <Container className="flex gap-[4px] mt-[8px] mb-[8px] pb-[10px] relative">
            <OrderBook tokenDetail={tokenData} />
            <OrderForm tokenDetail={tokenData} />
          </Container>
          <MovingLineTabs
            tabs={currentListTabs}
            onTabChange={handleChangeTab}
            defaultTab={currentTab}
            itemClassName="!px-[7.1px] font-normal"
          />
          {activeWallet?.isConnected && ServiceConfig.token ? handleRenderTab(currentTab) : ButtonConnectWallet()}
        </div>
        <TabInfo tokenData={tokenData} show={currentNavTab === navTabs[1].value} />
        <TabAI show={currentNavTab === navTabs[2].value} address={address} />

        {error?.graphQLErrors?.[0]?.extensions?.code === 'TOKEN_NOT_FOUND' && (
          <div className="fixed top-0 left-0 w-full h-full z-30 bg-[#111111] opacity-85">
            <div className="py-4 px-3 rounded-[8px] bg-[#27272a] w-100% min-w-[351px] max-w-[351px] h-fit mx-auto mt-4">
              <p className="text-sm">{t('detail.tokenDetail.processed')}</p>
              <div className="flex items-center gap-2 mt-1">
                <p className="text-sm">{t('detail.tokenDetail.waiting')} ...</p>
                <img src="/images/loading-sprite.png" className="w-6 h-6" alt="" />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default MemeDetailPage
